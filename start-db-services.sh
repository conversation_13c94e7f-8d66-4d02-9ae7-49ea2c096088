#!/bin/bash

# Onyx数据库服务器服务启动脚本
# 适用于双服务器部署架构中的数据库服务器 (10.0.83.36)
# 
# 使用方法:
# chmod +x start-db-services.sh
# ./start-db-services.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}========================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}========================================${NC}"
}

# 检查PostgreSQL服务状态
check_postgresql() {
    if systemctl is-active --quiet postgresql-15; then
        log_success "PostgreSQL服务运行正常"
        return 0
    else
        log_error "PostgreSQL服务未运行"
        return 1
    fi
}

# 检查容器是否存在
check_container_exists() {
    local container_name=$1
    if docker ps -a --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        return 0
    else
        return 1
    fi
}

# 启动PostgreSQL服务
start_postgresql() {
    log_header "🗄️ 启动PostgreSQL数据库服务"
    
    if check_postgresql; then
        log_info "PostgreSQL已在运行"
    else
        log_info "启动PostgreSQL服务..."
        sudo systemctl start postgresql-15
        sleep 5
        
        if check_postgresql; then
            log_success "PostgreSQL启动成功"
        else
            log_error "PostgreSQL启动失败"
            return 1
        fi
    fi
    
    # 测试数据库连接
    log_info "测试数据库连接..."
    if sudo -u postgres psql -c "SELECT 1;" > /dev/null 2>&1; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败"
        return 1
    fi
}

# 启动Redis服务
start_redis() {
    log_header "🔄 启动Redis缓存服务"
    
    if check_container_exists "km-redis"; then
        log_info "启动现有Redis容器..."
        docker start km-redis
    else
        log_info "创建并启动Redis容器..."
        docker run -d \
            --name km-redis \
            --restart unless-stopped \
            -p 6379:6379 \
            -v /opt/redis/data:/data \
            redis:7-alpine redis-server --appendonly yes
    fi
    
    sleep 5
    
    # 测试Redis连接
    log_info "测试Redis连接..."
    if docker exec km-redis redis-cli ping > /dev/null 2>&1; then
        log_success "Redis连接正常"
    else
        log_error "Redis连接失败"
        return 1
    fi
}

# 启动MinIO服务
start_minio() {
    log_header "📁 启动MinIO对象存储服务"
    
    # 创建数据目录
    sudo mkdir -p /opt/minio/data
    sudo chown -R 1000:1000 /opt/minio/data
    
    if check_container_exists "km-minio"; then
        log_info "启动现有MinIO容器..."
        docker start km-minio
    else
        log_info "创建并启动MinIO容器..."
        docker run -d \
            --name km-minio \
            --restart unless-stopped \
            -p 9001:9000 \
            -p 9002:9001 \
            -e MINIO_ROOT_USER=minioadmin \
            -e MINIO_ROOT_PASSWORD=minioadmin \
            -v /opt/minio/data:/data \
            minio/minio:RELEASE.2024-01-16T16-07-38Z \
            server /data --console-address ":9001"
    fi
    
    sleep 10
    
    # 测试MinIO连接
    log_info "测试MinIO连接..."
    if curl -f -s http://localhost:9001 > /dev/null 2>&1; then
        log_success "MinIO连接正常"
    else
        log_error "MinIO连接失败"
        return 1
    fi
}

# 显示服务状态
show_status() {
    log_header "📊 数据库服务器状态"
    
    echo "PostgreSQL服务状态:"
    systemctl status postgresql-15 --no-pager -l
    
    echo ""
    echo "Docker容器状态:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(km-|NAMES)"
    
    echo ""
    echo "服务健康检查:"
    
    # 检查PostgreSQL
    if check_postgresql; then
        log_success "✓ PostgreSQL运行正常"
    else
        log_error "✗ PostgreSQL连接失败"
    fi
    
    # 检查Redis
    if docker exec km-redis redis-cli ping > /dev/null 2>&1; then
        log_success "✓ Redis运行正常"
    else
        log_error "✗ Redis连接失败"
    fi
    
    # 检查MinIO
    if curl -f -s http://localhost:9001 > /dev/null 2>&1; then
        log_success "✓ MinIO运行正常"
    else
        log_error "✗ MinIO连接失败"
    fi
    
    echo ""
    echo "系统资源使用情况:"
    free -h
    df -h | grep -E "(Filesystem|/dev/)"
}

# 停止所有服务
stop_services() {
    log_header "🛑 停止所有服务"
    
    # 停止Docker容器
    local containers=("km-minio" "km-redis")
    for container in "${containers[@]}"; do
        if check_container_exists "$container"; then
            log_info "停止容器: $container"
            docker stop "$container" || log_warning "停止 $container 失败"
        fi
    done
    
    # 停止PostgreSQL
    log_info "停止PostgreSQL服务..."
    sudo systemctl stop postgresql-15
    
    log_success "所有服务已停止"
}

# 重启所有服务
restart_services() {
    log_header "🔄 重启所有服务"
    stop_services
    sleep 5
    start_all_services
}

# 启动所有服务
start_all_services() {
    log_header "🚀 启动数据库服务器所有服务"
    
    # 检查Docker是否运行
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker服务未运行，请先启动Docker"
        exit 1
    fi
    
    # 按顺序启动服务
    start_postgresql
    start_redis
    start_minio
    
    log_header "✅ 启动完成"
    log_success "所有服务启动完成！"
    log_info "🔗 服务访问信息:"
    log_info "  PostgreSQL: 10.0.83.36:5432"
    log_info "  Redis: 10.0.83.36:6379"
    log_info "  MinIO API: http://10.0.83.36:9001"
    log_info "  MinIO控制台: http://10.0.83.36:9002 (minioadmin/minioadmin)"
}

# 显示帮助信息
show_help() {
    echo "Onyx数据库服务器服务启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start    启动所有服务（默认）"
    echo "  stop     停止所有服务"
    echo "  restart  重启所有服务"
    echo "  status   显示服务状态"
    echo "  help     显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start    # 启动所有服务"
    echo "  $0 stop     # 停止所有服务"
    echo "  $0 status   # 查看服务状态"
}

# 处理命令行参数
case "${1:-start}" in
    "start")
        start_all_services
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        restart_services
        ;;
    "status")
        show_status
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    *)
        log_error "未知命令: $1"
        show_help
        exit 1
        ;;
esac
