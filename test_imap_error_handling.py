#!/usr/bin/env python3
"""
测试IMAP连接器的增强错误处理功能
"""

import time
import requests
import json
from datetime import datetime

def check_connector_status():
    """检查连接器状态"""
    try:
        response = requests.get("http://localhost:8080/api/manage/admin/connector", timeout=10)
        if response.status_code == 200:
            connectors = response.json()
            for connector in connectors:
                if connector.get('source') == 'email':
                    print(f"连接器: {connector.get('name')}")
                    print(f"状态: {connector.get('status')}")
                    print(f"最后索引时间: {connector.get('last_indexed')}")
                    print(f"索引文档数: {connector.get('total_docs_indexed', 0)}")
                    print("-" * 50)
            return connectors
        else:
            print(f"API请求失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"检查连接器状态失败: {e}")
        return None

def monitor_indexing_progress(duration_minutes=10):
    """监控索引进度"""
    print(f"开始监控索引进度，持续 {duration_minutes} 分钟...")
    print("=" * 60)
    
    start_time = time.time()
    end_time = start_time + (duration_minutes * 60)
    
    previous_docs = {}
    
    while time.time() < end_time:
        current_time = datetime.now().strftime("%H:%M:%S")
        print(f"\n[{current_time}] 检查连接器状态:")
        
        connectors = check_connector_status()
        if connectors:
            for connector in connectors:
                if connector.get('source') == 'email':
                    name = connector.get('name')
                    current_docs = connector.get('total_docs_indexed', 0)
                    
                    if name in previous_docs:
                        docs_added = current_docs - previous_docs[name]
                        if docs_added > 0:
                            print(f"✅ {name}: 新增 {docs_added} 个文档 (总计: {current_docs})")
                        elif current_docs == previous_docs[name]:
                            print(f"⏸️  {name}: 无新文档 (总计: {current_docs})")
                        else:
                            print(f"⚠️  {name}: 文档数量异常 (总计: {current_docs})")
                    else:
                        print(f"📊 {name}: 当前文档数 {current_docs}")
                    
                    previous_docs[name] = current_docs
        
        # 等待30秒后再次检查
        time.sleep(30)
    
    print(f"\n监控完成。总监控时间: {duration_minutes} 分钟")

def check_background_logs():
    """检查后台日志中的错误处理信息"""
    import subprocess
    
    print("检查最近的后台日志...")
    try:
        # 获取最近的日志
        result = subprocess.run(
            ["docker", "logs", "km-background", "--tail", "50"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            logs = result.stdout
            
            # 分析日志内容
            lines = logs.split('\n')
            error_lines = []
            success_lines = []
            
            for line in lines:
                if any(keyword in line.lower() for keyword in ['error', 'failed', 'exception']):
                    error_lines.append(line)
                elif any(keyword in line.lower() for keyword in ['success', 'processed', 'complete']):
                    success_lines.append(line)
            
            print(f"\n📊 日志分析结果:")
            print(f"错误相关日志: {len(error_lines)} 条")
            print(f"成功相关日志: {len(success_lines)} 条")
            
            if error_lines:
                print(f"\n❌ 最近的错误日志:")
                for line in error_lines[-5:]:  # 显示最近5条错误
                    print(f"  {line}")
            
            if success_lines:
                print(f"\n✅ 最近的成功日志:")
                for line in success_lines[-3:]:  # 显示最近3条成功
                    print(f"  {line}")
        else:
            print(f"获取日志失败: {result.stderr}")
    
    except subprocess.TimeoutExpired:
        print("获取日志超时")
    except Exception as e:
        print(f"获取日志失败: {e}")

def main():
    """主函数"""
    print("=== IMAP错误处理测试工具 ===")
    print()
    
    while True:
        print("\n请选择操作:")
        print("1. 检查连接器状态")
        print("2. 监控索引进度 (10分钟)")
        print("3. 检查后台日志")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            check_connector_status()
        elif choice == '2':
            monitor_indexing_progress(10)
        elif choice == '3':
            check_background_logs()
        elif choice == '4':
            print("退出测试工具")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
