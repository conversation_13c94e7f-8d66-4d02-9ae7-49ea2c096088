# Onyx知识管理系统 - 主要环境配置
# 基于docker-compose.main.yml的配置

# ===== 数据库配置 =====
DATABASE_URL=**********************************************/knowledge-manage
POSTGRES_HOST=**********
POSTGRES_PORT=5432
POSTGRES_USER=km_user
POSTGRES_PASSWORD=Sygy@2025
POSTGRES_DB=knowledge-manage

# ===== Redis配置 =====
REDIS_URL=redis://**********:6379/0
REDIS_HOST=**********
REDIS_PORT=6379
REDIS_DB_NUMBER=0
REDIS_DB_NUMBER_CELERY=1
REDIS_DB_NUMBER_CELERY_RESULT_BACKEND=2

# ===== Vespa搜索引擎配置 =====
VESPA_HOST=km-vespa
VESPA_PORT=8080
VESPA_URL=http://km-vespa:8080

# ===== 模型服务器配置 =====
MODEL_SERVER_HOST=km-model-server
MODEL_SERVER_PORT=9000
MODEL_SERVER_URL=http://km-model-server:9000

# ===== MinIO对象存储配置 =====
S3_ENDPOINT_URL=http://**********:9001
S3_AWS_ACCESS_KEY_ID=minioadmin
S3_AWS_SECRET_ACCESS_KEY=minioadmin123
S3_FILE_STORE_BUCKET_NAME=onyx-file-store
AWS_REGION_NAME=us-east-1

# ===== 应用配置 =====
PYTHONPATH=/app
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1
LOG_LEVEL=INFO

# ===== 认证配置 =====
AUTH_TYPE=disabled
SESSION_EXPIRE_TIME_SECONDS=604800

# ===== Celery配置 =====
C_FORCE_ROOT=1
CELERY_BROKER_URL=redis://**********:6379/1
CELERY_RESULT_BACKEND=redis://**********:6379/2

# ===== 安全配置 =====
ENCRYPTION_KEY_SECRET=your-secret-key-here
SECRET=your-secret-here

# ===== 开发配置 =====
DEV_MODE=true
DISABLE_TELEMETRY=true

# ===== 网络配置 =====
WEB_DOMAIN=http://localhost:3000
INTERNAL_URL=http://km-api-server:8080

# ===== 文件上传配置 =====
MAX_FILE_SIZE_MB=100
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,md,html

# ===== 邮件连接器配置 =====
# 这些将通过UI配置，这里仅作为参考
# IMAP_HOST=imap.example.com
# IMAP_PORT=993
# IMAP_USERNAME=<EMAIL>
# IMAP_PASSWORD=your-password
