#!/bin/bash

set -e

echo "🚀 开始启动主服务器所有服务..."

# 检查docker-compose.main.yml文件是否存在
if [ ! -f "docker-compose.main.yml" ]; then
    echo "❌ docker-compose.main.yml文件不存在"
    echo "请确保在项目根目录下运行此脚本"
    exit 1
fi

# 检查.env.main文件是否存在
if [ ! -f ".env.main" ]; then
    echo "❌ .env.main文件不存在"
    echo "请先运行deploy_main_server.sh脚本创建环境配置"
    exit 1
fi

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose -f docker-compose.main.yml down 2>/dev/null || true

# 第一步：启动Vespa搜索引擎
echo "📊 启动Vespa搜索引擎..."
docker-compose -f docker-compose.main.yml up -d vespa

# 等待Vespa启动完成（重要：Vespa需要较长启动时间）
echo "⏳ 等待Vespa启动完成（约2-3分钟）..."
timeout=180
elapsed=0
while ! curl -f http://localhost:8081/ApplicationStatus &>/dev/null; do
    sleep 10
    elapsed=$((elapsed + 10))
    echo "等待中... ${elapsed}/${timeout}秒"
    if [[ $elapsed -ge $timeout ]]; then
        echo "❌ Vespa启动超时，请检查日志"
        docker-compose -f docker-compose.main.yml logs vespa
        exit 1
    fi
done
echo "✅ Vespa搜索引擎启动成功"

# 第二步：启动AI模型服务器
echo "🧠 启动AI模型服务器..."
docker-compose -f docker-compose.main.yml up -d inference_model_server indexing_model_server

# 等待模型服务器启动
echo "⏳ 等待AI模型服务器启动（约1-2分钟）..."
sleep 60

# 检查推理模型服务器
timeout=120
elapsed=0
while ! curl -f http://localhost:9000/health &>/dev/null; do
    sleep 5
    elapsed=$((elapsed + 5))
    echo "等待推理模型服务器... ${elapsed}/${timeout}秒"
    if [[ $elapsed -ge $timeout ]]; then
        echo "⚠️ 推理模型服务器启动超时，继续下一步"
        break
    fi
done

# 检查索引模型服务器
timeout=120
elapsed=0
while ! curl -f http://localhost:9001/health &>/dev/null; do
    sleep 5
    elapsed=$((elapsed + 5))
    echo "等待索引模型服务器... ${elapsed}/${timeout}秒"
    if [[ $elapsed -ge $timeout ]]; then
        echo "⚠️ 索引模型服务器启动超时，继续下一步"
        break
    fi
done

echo "✅ AI模型服务器启动完成"

# 第三步：启动API后端服务
echo "⚙️ 启动API后端服务..."
docker-compose -f docker-compose.main.yml up -d api_server

# 等待API服务启动
echo "⏳ 等待API服务启动..."
timeout=120
elapsed=0
while ! curl -f http://localhost:8080/health &>/dev/null; do
    sleep 5
    elapsed=$((elapsed + 5))
    echo "等待中... ${elapsed}/${timeout}秒"
    if [[ $elapsed -ge $timeout ]]; then
        echo "❌ API服务启动超时，请检查日志"
        docker-compose -f docker-compose.main.yml logs api_server
        exit 1
    fi
done
echo "✅ API后端服务启动成功"

# 第四步：执行数据库迁移
echo "🗄️ 执行数据库迁移..."
if docker exec km-api-server alembic upgrade head; then
    echo "✅ 数据库迁移执行成功"
else
    echo "⚠️ 数据库迁移执行失败，请检查数据库连接"
fi

# 第五步：启动后台任务处理
echo "🔄 启动后台任务处理..."
docker-compose -f docker-compose.main.yml up -d background
sleep 10
echo "✅ 后台任务处理启动完成"

# 第六步：启动Web前端服务
echo "🌐 启动Web前端服务..."
docker-compose -f docker-compose.main.yml up -d web_server

# 等待前端服务启动
echo "⏳ 等待前端服务启动..."
timeout=60
elapsed=0
while ! curl -f http://localhost:3000 &>/dev/null; do
    sleep 5
    elapsed=$((elapsed + 5))
    echo "等待中... ${elapsed}/${timeout}秒"
    if [[ $elapsed -ge $timeout ]]; then
        echo "❌ 前端服务启动超时，请检查日志"
        docker-compose -f docker-compose.main.yml logs web_server
        exit 1
    fi
done
echo "✅ Web前端服务启动成功"

# 第七步：启动Nginx反向代理
echo "🔀 启动Nginx反向代理..."
docker-compose -f docker-compose.main.yml up -d nginx

# 等待Nginx启动
sleep 10

# 检查Nginx状态
if curl -f http://localhost/health &>/dev/null; then
    echo "✅ Nginx反向代理启动成功"
else
    echo "⚠️ Nginx反向代理可能有问题，请检查配置"
fi

echo "🎉 所有服务启动完成！"

# 显示服务状态
echo ""
echo "📋 服务状态检查："
docker-compose -f docker-compose.main.yml ps

echo ""
echo "🔍 服务健康检查："

# 检查各服务健康状态
services=(
    "Vespa搜索引擎:http://localhost:8081/ApplicationStatus"
    "推理模型服务器:http://localhost:9000/health"
    "索引模型服务器:http://localhost:9001/health"
    "API后端服务:http://localhost:8080/health"
    "Web前端服务:http://localhost:3000"
    "Nginx代理:http://localhost/health"
)

for service in "${services[@]}"; do
    name=$(echo $service | cut -d: -f1)
    url=$(echo $service | cut -d: -f2-)
    if curl -f "$url" &>/dev/null; then
        echo "✅ $name: 正常"
    else
        echo "❌ $name: 异常"
    fi
done

echo ""
echo "📱 访问地址："
echo "   主页面: http://**********"
echo "   API文档: http://**********/api/docs"
echo "   直接前端: http://**********:3000"
echo "   直接API: http://**********:8080"

echo ""
echo "📊 如需查看详细日志，请使用："
echo "   docker-compose -f docker-compose.main.yml logs [service-name]"

echo ""
echo "🎯 部署完成！系统已准备就绪。"
