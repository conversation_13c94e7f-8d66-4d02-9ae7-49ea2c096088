#!/bin/bash

# KM openEuler 20.03 LTS 双服务器集群部署脚本
# 适用于双服务器架构：主服务器(**********) + 数据库服务器(**********)
# 服务器要求: 每台CPU 8核, 内存 32GB, 磁盘 100GB+

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 项目配置
PROJECT_NAME="km"
MAIN_COMPOSE_FILE="docker-compose.main.yml"
DB_COMPOSE_FILE="docker-compose.db.yml"
MAIN_ENV_FILE=".env.main"
DB_ENV_FILE=".env.db"
LOG_FILE="deploy-cluster.log"

# 服务器配置
MAIN_SERVER="**********"
DB_SERVER="**********"
CURRENT_SERVER=$(hostname -I | awk '{print $1}')

# 服务列表（移除本地模型服务，使用外部模型）
MAIN_SERVICES=("vespa" "api_server" "background" "web_server" "nginx")
DB_SERVICES=("redis" "minio")

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

log_header() {
    echo -e "${CYAN}========================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}========================================${NC}"
}

# 检查Docker环境
check_docker() {
    log_info "检查Docker环境..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi

    log_success "Docker环境检查通过"
}

# 检查当前服务器类型
check_server_type() {
    log_info "检查当前服务器类型..."

    if [[ "$CURRENT_SERVER" == "$MAIN_SERVER" ]]; then
        log_success "当前在主服务器 ($MAIN_SERVER)"
        echo "main"
    elif [[ "$CURRENT_SERVER" == "$DB_SERVER" ]]; then
        log_success "当前在数据库服务器 ($DB_SERVER)"
        echo "db"
    else
        log_error "未识别的服务器IP: $CURRENT_SERVER"
        log_error "预期服务器: $MAIN_SERVER (主服务器) 或 $DB_SERVER (数据库服务器)"
        exit 1
    fi
}

# 检查项目文件
check_project_files() {
    log_info "检查项目文件..."

    local server_type=$(check_server_type)

    if [[ "$server_type" == "main" ]]; then
        if [ ! -f "$MAIN_COMPOSE_FILE" ]; then
            log_error "主服务器Docker Compose文件不存在: $MAIN_COMPOSE_FILE"
            exit 1
        fi
    elif [[ "$server_type" == "db" ]]; then
        if [ ! -f "$DB_COMPOSE_FILE" ]; then
            log_error "数据库服务器Docker Compose文件不存在: $DB_COMPOSE_FILE"
            exit 1
        fi
    fi

    log_success "项目文件检查通过"
}

# 创建环境配置文件
create_env_file() {
    log_info "创建环境配置文件..."

    local server_type=$(check_server_type)

    if [[ "$server_type" == "main" ]]; then
        if [ ! -f "$MAIN_ENV_FILE" ]; then
            log_info "创建主服务器环境配置文件..."
            cat > "$MAIN_ENV_FILE" << 'EOF'
# 数据库配置（连接到远程数据库服务器）
POSTGRES_HOST=**********
POSTGRES_PORT=5432
POSTGRES_USER=km_user
POSTGRES_PASSWORD=Sygy@2025
POSTGRES_DB=knowledge-manage

# Redis配置（连接到数据库服务器）
REDIS_HOST=**********
REDIS_PORT=6379
REDIS_PASSWORD=

# Vespa搜索引擎配置
VESPA_HOST=**********
VESPA_PORT=8081
VESPA_TENANT_PORT=19071

# MinIO对象存储配置（连接到数据库服务器）
S3_ENDPOINT_URL=http://**********:9001
S3_AWS_ACCESS_KEY_ID=minioadmin
S3_AWS_SECRET_ACCESS_KEY=minioadmin

# AI模型配置（使用外部模型服务）
GEN_AI_API_KEY=dummy-key
GEN_AI_API_ENDPOINT=http://***********:9997/qwen3/v1/chat/completions
GEN_AI_MODEL_NAME=qwen3:32b

# 嵌入模型配置（使用外部服务）
EMBEDDING_MODEL_SERVER_HOST=***********
EMBEDDING_MODEL_SERVER_PORT=9997
EMBEDDING_API_ENDPOINT=http://***********:9997/v1/embeddings
EMBEDDING_MODEL_NAME=bge-large-zh-v1.5

# 推理模型配置（使用外部服务）
INFERENCE_MODEL_SERVER_HOST=***********
INFERENCE_MODEL_SERVER_PORT=9997
INFERENCE_API_ENDPOINT=http://***********:9997/qwen3/v1/chat/completions
INFERENCE_MODEL_NAME=qwen3:32b

# Web服务配置
WEB_DOMAIN=http://**********:3000
CORS_ALLOWED_ORIGIN=http://**********
NEXT_PUBLIC_API_URL=http://**********:8080
NEXT_PUBLIC_DISABLE_STREAMING=false

# 认证配置
AUTH_TYPE=disabled
SECRET=your-secret-key-here-change-in-production
ENCRYPTION_KEY_SECRET=your-encryption-key-here-change-in-production

# 日志配置
LOG_LEVEL=info
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# 模型缓存配置
MODEL_CACHE_DIR=/app/model_cache
HF_HOME=/app/model_cache
TRANSFORMERS_CACHE=/app/model_cache

# Celery配置
CELERY_BROKER_URL=redis://**********:6379/0
CELERY_RESULT_BACKEND=redis://**********:6379/0
EOF
            log_warning "请根据需要编辑 $MAIN_ENV_FILE 文件（如API密钥等）"
        else
            log_info "主服务器环境配置文件已存在: $MAIN_ENV_FILE"
        fi

    elif [[ "$server_type" == "db" ]]; then
        if [ ! -f "$DB_ENV_FILE" ]; then
            log_info "创建数据库服务器环境配置文件..."
            cat > "$DB_ENV_FILE" << 'EOF'
# PostgreSQL配置（宿主机安装，此配置仅供参考）
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=km_user
POSTGRES_PASSWORD=Sygy@2025
POSTGRES_DB=knowledge-manage

# Redis配置
REDIS_HOST=0.0.0.0
REDIS_PORT=6379
REDIS_PASSWORD=

# MinIO对象存储配置
S3_ENDPOINT_URL=http://0.0.0.0:9001
S3_AWS_ACCESS_KEY_ID=minioadmin
S3_AWS_SECRET_ACCESS_KEY=minioadmin

# 日志配置
LOG_LEVEL=info
EOF
            log_info "数据库服务器环境配置文件创建完成: $DB_ENV_FILE"
        else
            log_info "数据库服务器环境配置文件已存在: $DB_ENV_FILE"
        fi
    fi

    log_success "环境配置文件准备完成"
}

# 初始化环境
setup_environment() {
    log_header "初始化双服务器集群部署环境"

    local server_type=$(check_server_type)

    check_docker
    check_project_files
    create_env_file

    log_info "当前服务器类型: $server_type"
    if [[ "$server_type" == "main" ]]; then
        log_info "主服务器环境初始化完成"
        log_info "请确保数据库服务器 ($DB_SERVER) 已完成部署"
    elif [[ "$server_type" == "db" ]]; then
        log_info "数据库服务器环境初始化完成"
        log_info "请先在此服务器完成PostgreSQL宿主机安装"
    fi

    log_success "环境初始化完成"
}

# 构建Docker镜像
build_images() {
    log_header "构建Docker镜像"

    local server_type=$(check_server_type)

    if [[ "$server_type" == "main" ]]; then
        log_info "开始构建主服务器镜像..."
        docker-compose -f "$MAIN_COMPOSE_FILE" build --parallel
    elif [[ "$server_type" == "db" ]]; then
        log_info "开始构建数据库服务器镜像..."
        docker-compose -f "$DB_COMPOSE_FILE" build --parallel
    fi

    log_success "Docker镜像构建完成"
}

# 启动所有服务
start_services() {
    log_header "启动KM集群服务"

    local server_type=$(check_server_type)

    if [[ "$server_type" == "main" ]]; then
        log_info "启动主服务器服务..."

        # 检查数据库服务器连接
        log_info "检查数据库服务器连接..."
        if ! ping -c 3 "$DB_SERVER" &> /dev/null; then
            log_error "无法连接到数据库服务器 $DB_SERVER"
            exit 1
        fi

        log_info "启动Vespa搜索引擎..."
        docker-compose -f "$MAIN_COMPOSE_FILE" --env-file "$MAIN_ENV_FILE" up -d vespa
        sleep 60  # Vespa需要较长启动时间

        log_info "启动应用服务（使用外部AI模型服务）..."
        docker-compose -f "$MAIN_COMPOSE_FILE" --env-file "$MAIN_ENV_FILE" up -d api_server background web_server nginx

    elif [[ "$server_type" == "db" ]]; then
        log_info "启动数据库服务器服务..."

        # 检查PostgreSQL是否运行
        if ! systemctl is-active --quiet postgresql-15; then
            log_error "PostgreSQL服务未运行，请先启动PostgreSQL"
            log_info "运行: sudo systemctl start postgresql-15"
            exit 1
        fi

        log_info "启动Redis和MinIO服务..."
        docker-compose -f "$DB_COMPOSE_FILE" --env-file "$DB_ENV_FILE" up -d
    fi

    log_success "服务启动完成"
}

# 停止所有服务
stop_services() {
    log_header "停止KM集群服务"

    local server_type=$(check_server_type)

    if [[ "$server_type" == "main" ]]; then
        log_info "停止主服务器服务..."
        docker-compose -f "$MAIN_COMPOSE_FILE" down
    elif [[ "$server_type" == "db" ]]; then
        log_info "停止数据库服务器服务..."
        docker-compose -f "$DB_COMPOSE_FILE" down
        log_warning "PostgreSQL服务仍在运行（宿主机安装）"
    fi

    log_success "服务已停止"
}

# 重启所有服务
restart_services() {
    log_header "重启KM集群服务"

    stop_services
    sleep 5
    start_services
}

# 部署服务（包含构建选项）
deploy_services() {
    log_header "部署KM双服务器集群"

    local server_type=$(check_server_type)
    local build_images=false
    if [ "$1" = "--build" ]; then
        build_images=true
    fi

    if [ "$build_images" = true ]; then
        build_images
    fi

    start_services

    # 等待服务完全启动
    log_info "等待服务完全启动..."
    sleep 30

    # 仅在主服务器执行数据库迁移
    if [[ "$server_type" == "main" ]]; then
        log_info "执行数据库迁移..."
        docker-compose -f "$MAIN_COMPOSE_FILE" exec -T api_server alembic upgrade head || log_warning "数据库迁移可能失败，请检查日志"
    fi

    log_success "KM集群部署完成"
    show_access_info
}

# 显示服务状态
show_status() {
    log_header "KM集群服务状态"

    local server_type=$(check_server_type)

    if [[ "$server_type" == "main" ]]; then
        log_info "主服务器Docker Compose服务状态:"
        docker-compose -f "$MAIN_COMPOSE_FILE" ps

        echo ""
        log_info "主服务器健康检查:"

        # 检查API服务
        if curl -s http://localhost:8080/health &> /dev/null; then
            log_success "✓ API服务运行正常"
        else
            log_warning "⚠ API服务可能未完全启动"
        fi

        # 检查前端服务
        if curl -s http://localhost:3000 &> /dev/null; then
            log_success "✓ 前端服务运行正常"
        else
            log_warning "⚠ 前端服务可能未完全启动"
        fi

        # 检查Vespa服务
        if curl -s http://localhost:8081/ApplicationStatus &> /dev/null; then
            log_success "✓ Vespa搜索引擎运行正常"
        else
            log_warning "⚠ Vespa搜索引擎可能未完全启动"
        fi

        # 检查数据库连接
        if PGPASSWORD="Sygy@2025" psql -h "$DB_SERVER" -U km_user -d "knowledge-manage" -c "SELECT 1;" &> /dev/null; then
            log_success "✓ 数据库连接正常"
        else
            log_warning "⚠ 数据库连接失败"
        fi

    elif [[ "$server_type" == "db" ]]; then
        log_info "数据库服务器Docker Compose服务状态:"
        docker-compose -f "$DB_COMPOSE_FILE" ps

        echo ""
        log_info "数据库服务器健康检查:"

        # 检查PostgreSQL
        if systemctl is-active --quiet postgresql-15; then
            log_success "✓ PostgreSQL服务运行正常"
        else
            log_error "✗ PostgreSQL服务未运行"
        fi

        # 检查Redis
        if docker exec km-redis redis-cli ping &> /dev/null; then
            log_success "✓ Redis缓存运行正常"
        else
            log_error "✗ Redis缓存连接失败"
        fi

        # 检查MinIO
        if curl -s http://localhost:9001 &> /dev/null; then
            log_success "✓ MinIO对象存储运行正常"
        else
            log_warning "⚠ MinIO对象存储可能未完全启动"
        fi
    fi
}

# 显示访问信息
show_access_info() {
    log_header "KM集群访问信息"

    local server_type=$(check_server_type)

    if [[ "$server_type" == "main" ]]; then
        log_info "🌐 主服务器访问地址:"
        log_info "  前端应用: http://$MAIN_SERVER"
        log_info "  API文档: http://$MAIN_SERVER/api/docs"
        log_info "  直接API: http://$MAIN_SERVER:8080"
        log_info "  直接前端: http://$MAIN_SERVER:3000"

        echo ""
        log_info "🔧 管理地址:"
        log_info "  Vespa控制台: http://$MAIN_SERVER:19071"

    elif [[ "$server_type" == "db" ]]; then
        log_info "🗄️ 数据库服务器访问地址:"
        log_info "  MinIO控制台: http://$DB_SERVER:9002 (minioadmin/minioadmin)"
    fi

    echo ""
    log_info "📊 集群连接信息:"
    log_info "  PostgreSQL: $DB_SERVER:5432 (km_user/Sygy@2025)"
    log_info "  Redis: $DB_SERVER:6379"
    log_info "  MinIO: $DB_SERVER:9001"
    log_info "  数据库名: knowledge-manage"
}

# 显示日志
show_logs() {
    local service="$1"
    local server_type=$(check_server_type)

    if [[ "$server_type" == "main" ]]; then
        if [ -n "$service" ]; then
            log_info "显示主服务器 $service 服务日志:"
            docker-compose -f "$MAIN_COMPOSE_FILE" logs -f "$service"
        else
            log_info "显示主服务器所有服务日志:"
            docker-compose -f "$MAIN_COMPOSE_FILE" logs -f
        fi
    elif [[ "$server_type" == "db" ]]; then
        if [ -n "$service" ]; then
            log_info "显示数据库服务器 $service 服务日志:"
            docker-compose -f "$DB_COMPOSE_FILE" logs -f "$service"
        else
            log_info "显示数据库服务器所有服务日志:"
            docker-compose -f "$DB_COMPOSE_FILE" logs -f
        fi
    fi
}

# 显示帮助信息
show_help() {
    echo "KM openEuler 20.03 LTS 双服务器集群部署脚本"
    echo ""
    echo "适用于双服务器架构部署"
    echo "主服务器 (**********): 应用服务、AI模型、搜索引擎"
    echo "数据库服务器 (**********): PostgreSQL、Redis、MinIO"
    echo "服务器要求: 每台CPU 8核, 内存 32GB, 磁盘 100GB+"
    echo ""
    echo "用法: $0 <命令> [选项]"
    echo ""
    echo "部署管理命令:"
    echo "  setup               初始化部署环境"
    echo "  deploy [--build]    部署所有服务（可选构建镜像）"
    echo "  start               启动所有服务"
    echo "  stop                停止所有服务"
    echo "  restart             重启所有服务"
    echo "  status              显示服务状态"
    echo "  logs [service]      显示日志（可指定服务名）"
    echo "  build               仅构建Docker镜像"
    echo ""
    echo "部署顺序:"
    echo "  1. 在数据库服务器 (**********) 上:"
    echo "     - 安装PostgreSQL: 参考部署文档"
    echo "     - 初始化环境: $0 setup"
    echo "     - 部署服务: $0 deploy"
    echo ""
    echo "  2. 在主服务器 (**********) 上:"
    echo "     - 初始化环境: $0 setup"
    echo "     - 部署服务: $0 deploy --build"
    echo ""
    echo "示例:"
    echo "  # 查看服务状态"
    echo "  $0 status"
    echo ""
    echo "  # 查看API服务日志"
    echo "  $0 logs api_server"
    echo ""
    echo "  # 重启所有服务"
    echo "  $0 restart"
}

# 主函数
main() {
    local command="$1"
    local option="$2"

    touch "$LOG_FILE"

    case "$command" in
        "setup")
            setup_environment
            ;;
        "deploy")
            setup_environment
            deploy_services "$option"
            ;;
        "build")
            build_images
            ;;
        "start")
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$option"
            ;;
        "help"|"--help"|"-h"|"")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

main "$@"
