#!/usr/bin/env python3
"""
IMAP连接测试脚本
用于测试本地邮箱的IMAP连接配置
"""

import imaplib
import ssl
import sys
from typing import Dict, List, Optional

class IMAPConnectionTester:
    def __init__(self):
        self.connection = None
    
    def test_connection(self, 
                       host: str, 
                       port: int, 
                       username: str, 
                       password: str,
                       use_ssl: bool = True) -> Dict[str, any]:
        """测试IMAP连接"""
        result = {
            "success": False,
            "message": "",
            "mailboxes": [],
            "error": None
        }
        
        try:
            print(f"🔗 正在连接到 {host}:{port}...")
            
            # 创建IMAP连接
            if use_ssl:
                self.connection = imaplib.IMAP4_SSL(host, port)
            else:
                self.connection = imaplib.IMAP4(host, port)
            
            print("✅ SSL连接建立成功")
            
            # 登录认证
            print(f"🔐 正在使用用户名 {username} 进行认证...")
            self.connection.login(username, password)
            print("✅ 认证成功")
            
            # 获取邮箱列表
            print("📁 正在获取邮箱文件夹列表...")
            status, mailboxes = self.connection.list()
            
            if status == 'OK':
                mailbox_list = []
                for mailbox in mailboxes:
                    # 解析邮箱名称
                    mailbox_str = mailbox.decode('utf-8')
                    # 提取邮箱名称（简化处理）
                    if '"' in mailbox_str:
                        mailbox_name = mailbox_str.split('"')[-2]
                    else:
                        mailbox_name = mailbox_str.split()[-1]
                    mailbox_list.append(mailbox_name)
                
                result["mailboxes"] = mailbox_list
                print(f"✅ 发现 {len(mailbox_list)} 个邮箱文件夹")
                
                # 测试选择INBOX
                print("📮 正在测试INBOX访问...")
                status, count = self.connection.select('INBOX')
                if status == 'OK':
                    email_count = int(count[0])
                    print(f"✅ INBOX访问成功，包含 {email_count} 封邮件")
                else:
                    print("⚠️ INBOX访问失败")
            
            result["success"] = True
            result["message"] = "IMAP连接测试成功"
            
        except imaplib.IMAP4.error as e:
            result["error"] = f"IMAP错误: {str(e)}"
            print(f"❌ IMAP错误: {e}")
        except ssl.SSLError as e:
            result["error"] = f"SSL错误: {str(e)}"
            print(f"❌ SSL错误: {e}")
        except Exception as e:
            result["error"] = f"连接错误: {str(e)}"
            print(f"❌ 连接错误: {e}")
        finally:
            if self.connection:
                try:
                    self.connection.logout()
                    print("🔚 连接已关闭")
                except:
                    pass
        
        return result
    
    def get_email_sample(self, 
                        host: str, 
                        port: int, 
                        username: str, 
                        password: str,
                        mailbox: str = 'INBOX',
                        count: int = 5) -> List[Dict]:
        """获取邮件样本用于测试"""
        emails = []
        
        try:
            # 连接和登录
            if port == 993:
                self.connection = imaplib.IMAP4_SSL(host, port)
            else:
                self.connection = imaplib.IMAP4(host, port)
            
            self.connection.login(username, password)
            self.connection.select(mailbox)
            
            # 搜索最新邮件
            status, messages = self.connection.search(None, 'ALL')
            if status == 'OK':
                message_ids = messages[0].split()
                # 获取最新的几封邮件
                latest_ids = message_ids[-count:] if len(message_ids) >= count else message_ids
                
                for msg_id in latest_ids:
                    status, msg_data = self.connection.fetch(msg_id, '(RFC822.HEADER)')
                    if status == 'OK':
                        header = msg_data[0][1].decode('utf-8', errors='ignore')
                        
                        # 简单解析邮件头
                        email_info = {
                            "id": msg_id.decode(),
                            "subject": self._extract_header(header, "Subject"),
                            "from": self._extract_header(header, "From"),
                            "date": self._extract_header(header, "Date"),
                        }
                        emails.append(email_info)
                        
        except Exception as e:
            print(f"❌ 获取邮件样本失败: {e}")
        finally:
            if self.connection:
                try:
                    self.connection.logout()
                except:
                    pass
        
        return emails
    
    def _extract_header(self, header_text: str, header_name: str) -> str:
        """提取邮件头信息"""
        lines = header_text.split('\n')
        for line in lines:
            if line.lower().startswith(header_name.lower() + ':'):
                return line.split(':', 1)[1].strip()
        return "Unknown"

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 Onyx IMAP连接器测试工具")
    print("=" * 60)
    
    # 测试配置 - 请根据您的邮箱修改这些配置
    test_configs = [
        {
            "name": "腾讯企业邮箱",
            "host": "imap.exmail.qq.com",
            "port": 993,
            "username": "<EMAIL>",  # 请修改
            "password": "your-password",           # 请修改
        },
        {
            "name": "Microsoft 365",
            "host": "outlook.office365.com", 
            "port": 993,
            "username": "<EMAIL>",  # 请修改
            "password": "your-app-password",       # 请修改
        },
        {
            "name": "Gmail",
            "host": "imap.gmail.com",
            "port": 993,
            "username": "<EMAIL>",    # 请修改
            "password": "your-app-password",       # 请修改
        }
    ]
    
    tester = IMAPConnectionTester()
    
    # 如果提供了命令行参数，使用自定义配置
    if len(sys.argv) >= 5:
        custom_config = {
            "name": "自定义配置",
            "host": sys.argv[1],
            "port": int(sys.argv[2]),
            "username": sys.argv[3],
            "password": sys.argv[4]
        }
        test_configs = [custom_config]
    
    for config in test_configs:
        print(f"\n🧪 测试配置: {config['name']}")
        print("-" * 40)
        
        # 跳过未配置的测试
        if "your-email" in config["username"] or "your-password" in config["password"]:
            print("⏭️ 跳过未配置的测试（请修改配置）")
            continue
        
        # 执行连接测试
        result = tester.test_connection(
            host=config["host"],
            port=config["port"],
            username=config["username"],
            password=config["password"]
        )
        
        if result["success"]:
            print(f"✅ {config['name']} 连接测试成功！")
            print(f"📁 可用邮箱文件夹: {', '.join(result['mailboxes'])}")
            
            # 获取邮件样本
            print("\n📧 获取邮件样本...")
            emails = tester.get_email_sample(
                host=config["host"],
                port=config["port"],
                username=config["username"],
                password=config["password"]
            )
            
            if emails:
                print(f"✅ 成功获取 {len(emails)} 封邮件样本")
                for i, email in enumerate(emails, 1):
                    print(f"  {i}. {email['subject']} (来自: {email['from']})")
            else:
                print("⚠️ 未能获取邮件样本")
        else:
            print(f"❌ {config['name']} 连接测试失败")
            print(f"错误: {result['error']}")
    
    print("\n" + "=" * 60)
    print("🎯 测试完成！")
    print("💡 提示：如果测试成功，您可以在Onyx中使用相同的配置")
    print("=" * 60)

if __name__ == "__main__":
    main()
