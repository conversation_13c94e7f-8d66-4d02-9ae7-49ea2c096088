#!/bin/bash

# Docker构建优化脚本
# 使用缓存和分阶段构建来提升效率

set -e

# 启用BuildKit
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

# 创建日志目录
mkdir -p ./logs

# 获取当前时间戳
TIMESTAMP=$(date +"%Y%m%d%H%M")
LOG_FILE="./logs/build-optimized-${TIMESTAMP}.log"

echo "🚀 开始优化构建过程..."
echo "📝 日志文件: ${LOG_FILE}"

# 函数：构建单个服务
build_service() {
    local service=$1
    echo "🔨 构建服务: ${service}"
    
    if docker compose -f docker-compose.main.yml build ${service} >> ${LOG_FILE} 2>&1; then
        echo "✅ ${service} 构建成功"
    else
        echo "❌ ${service} 构建失败，查看日志: ${LOG_FILE}"
        tail -n 50 ${LOG_FILE}
        exit 1
    fi
}

# 函数：检查服务是否需要重新构建
needs_rebuild() {
    local service=$1
    # 检查镜像是否存在
    if docker images | grep -q "knowledgemanageronyx[_-]${service}"; then
        echo "📦 ${service} 镜像已存在，跳过构建"
        return 1
    else
        echo "🆕 ${service} 需要构建"
        return 0
    fi
}

echo "🔍 检查现有镜像..."

# 按依赖顺序构建服务
services=("vespa" "api_server" "background" "web_server" "nginx")

for service in "${services[@]}"; do
    if needs_rebuild ${service} || [ "$1" == "--force" ]; then
        build_service ${service}
    fi
done

echo "🎉 所有服务构建完成！"
echo "📊 构建统计:"
docker images | grep knowledgemanageronyx

# 清理悬空镜像
echo "🧹 清理悬空镜像..."
docker image prune -f >> ${LOG_FILE} 2>&1

echo "✨ 构建优化完成！"
echo "💡 下次如果只有web_server出错，可以运行:"
echo "   docker compose -f docker-compose.main.yml build web_server"
