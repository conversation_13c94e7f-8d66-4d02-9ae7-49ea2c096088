#!/bin/bash

# Onyx主服务器服务启动脚本
# 适用于双服务器部署架构中的主服务器 (**********)
# 
# 使用方法:
# chmod +x start-main-services.sh
# ./start-main-services.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}========================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}========================================${NC}"
}

# 检查容器是否存在
check_container_exists() {
    local container_name=$1
    if docker ps -a --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        return 0
    else
        return 1
    fi
}

# 启动或创建容器
start_or_create_container() {
    local container_name=$1
    local create_command=$2
    
    if check_container_exists "$container_name"; then
        log_info "启动现有容器: $container_name"
        docker start "$container_name" || log_error "启动容器 $container_name 失败"
    else
        log_warning "容器 $container_name 不存在，请先按照文档说明创建容器"
        log_info "创建命令示例: $create_command"
    fi
}

# 检查服务健康状态
check_service_health() {
    local service_name=$1
    local health_url=$2
    local max_retries=30
    local retry_count=0
    
    log_info "检查 $service_name 健康状态..."
    
    while [ $retry_count -lt $max_retries ]; do
        if curl -f -s "$health_url" > /dev/null 2>&1; then
            log_success "$service_name 运行正常"
            return 0
        fi
        
        retry_count=$((retry_count + 1))
        log_info "等待 $service_name 启动... ($retry_count/$max_retries)"
        sleep 2
    done
    
    log_error "$service_name 健康检查失败"
    return 1
}

# 主函数
main() {
    log_header "🚀 启动Onyx主服务器所有服务"
    
    # 检查Docker是否运行
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker服务未运行，请先启动Docker"
        exit 1
    fi
    
    # 1. 启动Vespa搜索引擎
    log_header "📊 启动Vespa搜索引擎"
    start_or_create_container "km-vespa" "docker run -d --name km-vespa --restart unless-stopped -p 8081:8080 -p 19071:19071 -v /opt/vespa/data:/opt/vespa/var vespaengine/vespa:8.526.15"
    sleep 30
    check_service_health "Vespa搜索引擎" "http://localhost:8081/ApplicationStatus"
    
    # 2. 启动推理模型服务器
    log_header "🧠 启动推理模型服务器"
    start_or_create_container "km-inference-model" "请参考文档构建推理模型镜像并创建容器"
    sleep 20
    check_service_health "推理模型服务器" "http://localhost:9000/health"
    
    # 3. 启动索引模型服务器
    log_header "📚 启动索引模型服务器"
    start_or_create_container "km-indexing-model" "请参考文档构建索引模型镜像并创建容器"
    sleep 20
    check_service_health "索引模型服务器" "http://localhost:9001/health"
    
    # 4. 启动API后端服务
    log_header "⚙️ 启动API后端服务"
    start_or_create_container "km-api-server" "请参考文档构建API后端镜像并创建容器"
    sleep 15
    check_service_health "API后端服务" "http://localhost:8080/health"
    
    # 5. 启动后台任务处理
    log_header "🔄 启动后台任务处理"
    start_or_create_container "km-background" "请参考文档创建后台任务容器"
    sleep 10
    
    # 6. 启动Web前端服务
    log_header "🌐 启动Web前端服务"
    start_or_create_container "km-web-server" "请参考文档构建前端镜像并创建容器"
    sleep 15
    check_service_health "Web前端服务" "http://localhost:3000"
    
    # 7. 启动Nginx代理
    log_header "🔀 启动Nginx反向代理"
    start_or_create_container "km-nginx" "请参考文档创建Nginx容器"
    sleep 5
    check_service_health "Nginx反向代理" "http://localhost"
    
    # 显示最终状态
    log_header "📊 服务状态总览"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep km-
    
    log_header "✅ 启动完成"
    log_success "所有服务启动完成！"
    log_info "🌐 访问地址:"
    log_info "  主页: http://**********"
    log_info "  API文档: http://**********/api/docs"
    log_info "  直接前端: http://**********:3000"
    log_info "  直接API: http://**********:8080"
    log_info "  Vespa控制台: http://**********:19071"
}

# 显示帮助信息
show_help() {
    echo "Onyx主服务器服务启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start    启动所有服务（默认）"
    echo "  stop     停止所有服务"
    echo "  restart  重启所有服务"
    echo "  status   显示服务状态"
    echo "  help     显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start    # 启动所有服务"
    echo "  $0 stop     # 停止所有服务"
    echo "  $0 status   # 查看服务状态"
}

# 停止所有服务
stop_services() {
    log_header "🛑 停止所有服务"
    
    local services=("km-nginx" "km-web-server" "km-background" "km-api-server" "km-indexing-model" "km-inference-model" "km-vespa")
    
    for service in "${services[@]}"; do
        if check_container_exists "$service"; then
            log_info "停止服务: $service"
            docker stop "$service" || log_warning "停止 $service 失败"
        else
            log_info "服务 $service 不存在，跳过"
        fi
    done
    
    log_success "所有服务已停止"
}

# 显示服务状态
show_status() {
    log_header "📊 服务状态"
    
    echo "Docker容器状态:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(km-|NAMES)"
    
    echo ""
    echo "服务健康检查:"
    
    # 检查各个服务
    local services=(
        "Vespa搜索引擎:http://localhost:8081/ApplicationStatus"
        "推理模型服务器:http://localhost:9000/health"
        "索引模型服务器:http://localhost:9001/health"
        "API后端服务:http://localhost:8080/health"
        "Web前端服务:http://localhost:3000"
        "Nginx代理:http://localhost"
    )
    
    for service_info in "${services[@]}"; do
        local service_name=$(echo "$service_info" | cut -d: -f1)
        local service_url=$(echo "$service_info" | cut -d: -f2-)
        
        if curl -f -s "$service_url" > /dev/null 2>&1; then
            log_success "✓ $service_name 运行正常"
        else
            log_error "✗ $service_name 连接失败"
        fi
    done
}

# 重启所有服务
restart_services() {
    log_header "🔄 重启所有服务"
    stop_services
    sleep 5
    main
}

# 处理命令行参数
case "${1:-start}" in
    "start")
        main
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        restart_services
        ;;
    "status")
        show_status
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    *)
        log_error "未知命令: $1"
        show_help
        exit 1
        ;;
esac
