#!/usr/bin/env python3
"""
IMAP连接器健壮性测试脚本
用于诊断和修复multipart数据解析问题
"""

import imaplib
import email
import traceback
from email.message import Message
from typing import Optional
import time

class RobustIMAPTester:
    def __init__(self, host: str, port: int, username: str, password: str):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.mail_client: Optional[imaplib.IMAP4_SSL] = None
    
    def connect(self) -> bool:
        """建立IMAP连接"""
        try:
            print(f"连接到 {self.host}:{self.port}")
            self.mail_client = imaplib.IMAP4_SSL(self.host, self.port)
            
            print("尝试登录...")
            status, data = self.mail_client.login(self.username, self.password)
            
            if status == 'OK':
                print("✅ IMAP连接成功")
                return True
            else:
                print(f"❌ 登录失败: {status}")
                return False
                
        except Exception as e:
            print(f"❌ 连接错误: {e}")
            return False
    
    def list_mailboxes(self) -> list:
        """列出所有邮箱"""
        if not self.mail_client:
            return []
        
        try:
            status, mailboxes = self.mail_client.list()
            if status == 'OK':
                print(f"✅ 发现 {len(mailboxes)} 个邮箱")
                for mailbox in mailboxes[:5]:  # 只显示前5个
                    print(f"  - {mailbox.decode()}")
                return mailboxes
            else:
                print(f"❌ 获取邮箱列表失败: {status}")
                return []
        except Exception as e:
            print(f"❌ 邮箱列表错误: {e}")
            return []
    
    def test_mailbox_access(self, mailbox: str = "INBOX") -> bool:
        """测试邮箱访问"""
        if not self.mail_client:
            return False
        
        try:
            print(f"选择邮箱: {mailbox}")
            status, data = self.mail_client.select(mailbox)
            
            if status == 'OK':
                message_count = int(data[0])
                print(f"✅ 邮箱访问成功，共 {message_count} 封邮件")
                return True
            else:
                print(f"❌ 邮箱访问失败: {status}")
                return False
                
        except Exception as e:
            print(f"❌ 邮箱访问错误: {e}")
            return False
    
    def test_email_fetch(self, mailbox: str = "INBOX", limit: int = 5) -> int:
        """测试邮件获取和解析"""
        if not self.mail_client:
            return 0
        
        try:
            # 选择邮箱
            self.mail_client.select(mailbox)
            
            # 搜索最近的邮件
            status, email_ids = self.mail_client.search(None, 'ALL')
            if status != 'OK':
                print(f"❌ 邮件搜索失败: {status}")
                return 0
            
            ids = email_ids[0].split()
            if not ids:
                print("📭 邮箱为空")
                return 0
            
            # 测试最近的几封邮件
            test_ids = ids[-limit:] if len(ids) >= limit else ids
            success_count = 0
            
            print(f"测试 {len(test_ids)} 封邮件的解析...")
            
            for i, email_id in enumerate(test_ids, 1):
                try:
                    print(f"  [{i}/{len(test_ids)}] 处理邮件 ID: {email_id.decode()}")
                    
                    # 获取邮件
                    status, msg_data = self.mail_client.fetch(email_id, '(RFC822)')
                    if status != 'OK':
                        print(f"    ❌ 获取失败: {status}")
                        continue
                    
                    # 解析邮件
                    raw_email = msg_data[0][1]
                    email_msg = email.message_from_bytes(raw_email)
                    
                    # 测试multipart解析
                    self._test_multipart_parsing(email_msg, email_id.decode())
                    success_count += 1
                    print(f"    ✅ 解析成功")
                    
                except Exception as e:
                    print(f"    ❌ 解析错误: {e}")
                    print(f"    详细错误: {traceback.format_exc()}")
                    continue
            
            print(f"✅ 成功解析 {success_count}/{len(test_ids)} 封邮件")
            return success_count
            
        except Exception as e:
            print(f"❌ 邮件获取测试错误: {e}")
            return 0
    
    def _test_multipart_parsing(self, email_msg: Message, email_id: str):
        """测试multipart解析（这是出错的关键部分）"""
        try:
            subject = email_msg.get('Subject', '无主题')
            print(f"    主题: {subject}")
            
            # 遍历邮件部分（这里可能出现multipart错误）
            part_count = 0
            for part in email_msg.walk():
                part_count += 1
                
                if part.is_multipart():
                    print(f"    发现multipart容器 (部分 {part_count})")
                    continue
                
                # 获取内容类型
                content_type = part.get_content_type()
                print(f"    部分 {part_count}: {content_type}")
                
                # 尝试获取payload（这里最容易出错）
                try:
                    charset = part.get_content_charset() or "utf-8"
                    raw_payload = part.get_payload(decode=True)
                    
                    if isinstance(raw_payload, bytes):
                        # 尝试解码
                        decoded_content = raw_payload.decode(charset, errors='ignore')
                        content_length = len(decoded_content)
                        print(f"    内容长度: {content_length} 字符")
                        
                        # 如果是文本内容，显示前50个字符
                        if content_type.startswith('text/'):
                            preview = decoded_content[:50].replace('\n', ' ')
                            print(f"    内容预览: {preview}...")
                    else:
                        print(f"    非字节内容: {type(raw_payload)}")
                        
                except Exception as payload_error:
                    print(f"    ⚠️  payload解析警告: {payload_error}")
                    # 不抛出异常，继续处理其他部分
                    
        except Exception as e:
            print(f"    ❌ multipart解析错误: {e}")
            raise  # 重新抛出以便上层处理
    
    def disconnect(self):
        """断开连接"""
        if self.mail_client:
            try:
                self.mail_client.logout()
                print("✅ 连接已断开")
            except:
                pass

def main():
    """主测试函数"""
    print("=== IMAP连接器健壮性测试 ===")
    print()
    
    # 配置信息
    host = "imap.139.com"
    port = 993
    username = "<EMAIL>"
    password = input("请输入您的授权码: ").strip()
    
    if not password:
        print("❌ 未提供授权码")
        return
    
    # 创建测试器
    tester = RobustIMAPTester(host, port, username, password)
    
    try:
        # 测试连接
        if not tester.connect():
            return
        
        # 测试邮箱列表
        mailboxes = tester.list_mailboxes()
        if not mailboxes:
            return
        
        # 测试INBOX访问
        if not tester.test_mailbox_access("INBOX"):
            return
        
        # 测试邮件解析
        success_count = tester.test_email_fetch("INBOX", limit=10)
        
        print()
        print("=== 测试总结 ===")
        if success_count > 0:
            print(f"✅ 基本功能正常，成功解析了 {success_count} 封邮件")
            print("建议：重启Onyx后台服务，问题可能是临时的")
        else:
            print("❌ 邮件解析存在问题，需要进一步调试")
            print("建议：检查网络连接和邮件服务器配置")
    
    finally:
        tester.disconnect()

if __name__ == "__main__":
    main()
