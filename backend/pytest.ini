[pytest]
pythonpath = 
    .
    generated/onyx_openapi_client
markers =
    slow: marks tests as slow
filterwarnings =
    ignore::DeprecationWarning
    ignore::cryptography.utils.CryptographyDeprecationWarning
    ignore::PendingDeprecationWarning:ddtrace.internal.module
# .test.env is gitignored.
# After installing pytest-dotenv,
# you can use it to test credentials locally.
env_files =
    .test.env
