[program:zmirror-USERNAME]
command=/home/<USER>/python-zulip-api/zulip/integrations/zephyr/zephyr_mirror_backend.py --stamp-path=/home/<USER>/zulip --user=USERNAME --log-path=/home/<USER>/logs/mirror-log-%(program_name)s --use-sessions --session-path=/home/<USER>/zephyr_sessions/%(program_name)s --api-key-file=/home/<USER>/api-keys/%(program_name)s --ignore-expired-tickets --nagios-path=/home/<USER>/mirror_status/%(program_name)s --nagios-class=zulip-mirror-nagios --site=https://zephyr.zulipchat.com
priority=200                   ; the relative start priority (default 999)
autostart=true                 ; start at supervisord start (default: true)
autorestart=true               ; whether/when to restart (default: unexpected)
stopsignal=TERM                 ; signal used to kill process (default TERM)
stopwaitsecs=30                ; max num secs to wait b4 SIGKILL (default 10)
user=zulip                    ; setuid to this UNIX account to run the program
redirect_stderr=true           ; redirect proc stderr to stdout (default false)
stdout_logfile=/var/log/zulip/%(program_name)s.log         ; stdout log path, NONE for none; default AUTO
environment=HOME="/home/<USER>",USER="zulip",KRB5CCNAME="/home/<USER>/ccache/%(program_name)s"
