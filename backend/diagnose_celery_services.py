#!/usr/bin/env python3
"""
诊断脚本：检查Onyx系统中Celery后台服务的状态
用于排查连接器索引任务不启动的问题
"""

import subprocess


def run_command(cmd: str, shell: bool = True) -> tuple[int, str, str]:
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, 
            shell=shell, 
            capture_output=True, 
            text=True, 
            timeout=30
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timeout"
    except Exception as e:
        return -1, "", str(e)

def check_docker_services():
    """检查Docker服务状态"""
    print("=== Docker服务状态检查 ===")
    
    # 检查Docker是否运行
    code, stdout, stderr = run_command("docker --version")
    if code != 0:
        print("❌ Docker未安装或未运行")
        return False
    else:
        print(f"✅ Docker版本: {stdout.strip()}")
    
    # 检查Docker Compose服务
    code, stdout, stderr = run_command("docker compose ps")
    if code == 0:
        print("📋 Docker Compose服务状态:")
        print(stdout)
    else:
        print(f"❌ 无法获取Docker Compose状态: {stderr}")
    
    return True

def check_celery_workers():
    """检查Celery Worker状态"""
    print("\n=== Celery Worker状态检查 ===")
    
    # 检查Celery inspect
    celery_commands = [
        "celery -A onyx.background.celery.versioned_apps.primary inspect active",
        "celery -A onyx.background.celery.versioned_apps.primary inspect stats",
        "celery -A onyx.background.celery.versioned_apps.primary inspect registered",
    ]
    
    for cmd in celery_commands:
        print(f"\n🔍 执行: {cmd}")
        code, stdout, stderr = run_command(cmd)
        if code == 0:
            print("✅ 成功:")
            print(stdout[:500] + "..." if len(stdout) > 500 else stdout)
        else:
            print(f"❌ 失败: {stderr}")

def check_redis_connection():
    """检查Redis连接"""
    print("\n=== Redis连接检查 ===")
    
    redis_commands = [
        "redis-cli ping",
        "redis-cli info server",
        "redis-cli keys 'onyx:*' | head -10",
    ]
    
    for cmd in redis_commands:
        print(f"\n🔍 执行: {cmd}")
        code, stdout, stderr = run_command(cmd)
        if code == 0:
            print("✅ 成功:")
            print(stdout[:200] + "..." if len(stdout) > 200 else stdout)
        else:
            print(f"❌ 失败: {stderr}")

def check_postgres_connection():
    """检查PostgreSQL连接"""
    print("\n=== PostgreSQL连接检查 ===")
    
    # 尝试连接数据库
    pg_commands = [
        "psql -h localhost -U postgres -d onyx -c '\\dt' -t",
        "psql -h localhost -U postgres -d onyx -c 'SELECT COUNT(*) FROM connector_credential_pair;'",
        "psql -h localhost -U postgres -d onyx -c 'SELECT id, status FROM index_attempt ORDER BY time_created DESC LIMIT 5;'",
    ]
    
    for cmd in pg_commands:
        print(f"\n🔍 执行: {cmd}")
        code, stdout, stderr = run_command(cmd)
        if code == 0:
            print("✅ 成功:")
            print(stdout[:300] + "..." if len(stdout) > 300 else stdout)
        else:
            print(f"❌ 失败: {stderr}")

def check_log_files():
    """检查日志文件"""
    print("\n=== 日志文件检查 ===")
    
    log_files = [
        "/var/log/celery_primary.log",
        "/var/log/celery_beat.log",
        "/var/log/celery_docfetching.log",
        "/var/log/celery_docprocessing.log",
        "/var/log/celery_heavy.log",
        "/var/log/celery_light.log",
    ]
    
    for log_file in log_files:
        print(f"\n📄 检查日志: {log_file}")
        code, stdout, stderr = run_command(f"tail -20 {log_file}")
        if code == 0:
            print("✅ 最近20行:")
            print(stdout)
        else:
            print(f"❌ 无法读取: {stderr}")

def check_supervisor_status():
    """检查Supervisor状态"""
    print("\n=== Supervisor状态检查 ===")
    
    supervisor_commands = [
        "supervisorctl status",
        "supervisorctl tail celery_primary",
        "supervisorctl tail celery_beat",
    ]
    
    for cmd in supervisor_commands:
        print(f"\n🔍 执行: {cmd}")
        code, stdout, stderr = run_command(cmd)
        if code == 0:
            print("✅ 成功:")
            print(stdout[:500] + "..." if len(stdout) > 500 else stdout)
        else:
            print(f"❌ 失败: {stderr}")

def check_environment_variables():
    """检查环境变量"""
    print("\n=== 环境变量检查 ===")
    
    important_vars = [
        "POSTGRES_HOST",
        "POSTGRES_PORT", 
        "POSTGRES_DB",
        "POSTGRES_USER",
        "REDIS_HOST",
        "REDIS_PORT",
        "CELERY_BROKER_URL",
        "CELERY_RESULT_BACKEND",
    ]
    
    for var in important_vars:
        code, stdout, stderr = run_command(f"echo ${var}")
        if stdout.strip():
            print(f"✅ {var}: {stdout.strip()}")
        else:
            print(f"❌ {var}: 未设置")

def check_network_connectivity():
    """检查网络连接"""
    print("\n=== 网络连接检查 ===")
    
    # 检查端口连接
    ports_to_check = [
        ("localhost", "5432", "PostgreSQL"),
        ("localhost", "6379", "Redis"),
        ("localhost", "8080", "Onyx Web"),
    ]
    
    for host, port, service in ports_to_check:
        print(f"\n🔍 检查 {service} ({host}:{port})")
        code, stdout, stderr = run_command(f"nc -z {host} {port}")
        if code == 0:
            print(f"✅ {service} 端口可达")
        else:
            print(f"❌ {service} 端口不可达")

def main():
    """主函数"""
    print("🔍 Onyx Celery服务诊断工具")
    print("=" * 50)
    
    try:
        # 执行各项检查
        check_docker_services()
        check_environment_variables()
        check_network_connectivity()
        check_redis_connection()
        check_postgres_connection()
        check_supervisor_status()
        check_celery_workers()
        check_log_files()
        
        print("\n" + "=" * 50)
        print("🏁 诊断完成")
        print("\n💡 建议检查项目:")
        print("1. 确保所有Docker容器都在运行")
        print("2. 检查Celery worker是否正确启动")
        print("3. 验证Redis和PostgreSQL连接")
        print("4. 查看日志文件中的错误信息")
        print("5. 确认环境变量配置正确")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 诊断被用户中断")
    except Exception as e:
        print(f"\n\n❌ 诊断过程中出现错误: {e}")

if __name__ == "__main__":
    main()
