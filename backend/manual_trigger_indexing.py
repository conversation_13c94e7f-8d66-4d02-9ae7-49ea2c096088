#!/usr/bin/env python3
"""
手动触发索引任务脚本
用于手动启动连接器的索引任务，绕过自动调度机制
"""

import os
import subprocess
import sys
import time


def setup_python_path():
    """设置Python路径以便导入onyx模块"""
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # 可能的项目根目录
    possible_roots = [
        script_dir,  # backend目录
        os.path.dirname(script_dir),  # 项目根目录
        os.path.join(os.path.dirname(script_dir), "backend"),  # 如果在项目根目录下
    ]

    for root in possible_roots:
        if root not in sys.path:
            sys.path.insert(0, root)

        # 检查是否能找到onyx模块
        onyx_path = os.path.join(root, "onyx")
        if os.path.exists(onyx_path) and os.path.isdir(onyx_path):
            print(f"✅ 找到onyx模块路径: {onyx_path}")
            return True

    return False

def check_environment():
    """检查运行环境"""
    print("=== 环境检查 ===")

    # 检查Python版本
    print(f"Python版本: {sys.version}")

    # 检查当前工作目录
    print(f"当前工作目录: {os.getcwd()}")

    # 检查Python路径
    print("Python路径:")
    for path in sys.path[:5]:  # 只显示前5个
        print(f"  {path}")

    return True

# 设置路径
if not setup_python_path():
    print("❌ 无法找到onyx模块路径")
    print("请确保在正确的目录下运行此脚本，或设置PYTHONPATH环境变量")
    sys.exit(1)

# 尝试导入onyx模块
try:
    from onyx.db.engine import get_session_with_current_tenant
    from onyx.db.connector_credential_pair import get_connector_credential_pairs
    from onyx.db.index_attempt import get_latest_index_attempt
    from onyx.background.celery.celery_app import celery_app
    from onyx.background.celery.tasks.shared import OnyxCeleryTask, OnyxCeleryPriority
    from onyx.utils.logger import setup_logger

    logger = setup_logger()
    IMPORTS_SUCCESSFUL = True
    print("✅ 所有模块导入成功")

except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    print("\n💡 可能的解决方案:")
    print("1. 确保在backend目录下运行: cd /path/to/onyx/backend")
    print("2. 设置PYTHONPATH: export PYTHONPATH=/path/to/onyx/backend:$PYTHONPATH")
    print("3. 激活虚拟环境: source venv/bin/activate")
    print("4. 安装依赖: pip install -r requirements.txt")
    print("\n🔄 将使用数据库直接查询模式...")
    IMPORTS_SUCCESSFUL = False
    logger = None

def run_sql_query(query: str, description: str = ""):
    """运行SQL查询"""
    if description:
        print(f"=== {description} ===")

    print(f"🔍 执行SQL查询:")
    print(f"```sql")
    print(query)
    print(f"```")
    print()

    # 尝试使用psql命令执行查询
    try:
        cmd = [
            "psql",
            "-h", "**********",
            "-U", "km_user",
            "-d", "knowledge-manage",
            "-c", query
        ]

        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30
        )

        if result.returncode == 0:
            print("✅ 查询成功:")
            print(result.stdout)
        else:
            print("❌ 查询失败:")
            print(result.stderr)
            print("\n💡 请手动在数据库中执行上述SQL查询")

    except subprocess.TimeoutExpired:
        print("❌ 查询超时")
    except FileNotFoundError:
        print("❌ 未找到psql命令")
        print("💡 请手动在数据库中执行上述SQL查询")
    except Exception as e:
        print(f"❌ 执行查询时出错: {e}")
        print("💡 请手动在数据库中执行上述SQL查询")

def list_connector_credential_pairs():
    """列出所有连接器凭证对"""
    if IMPORTS_SUCCESSFUL:
        # 使用Python API
        print("=== 连接器凭证对列表 (Python API) ===")

        try:
            with get_session_with_current_tenant() as db_session:
                cc_pairs = get_connector_credential_pairs(db_session)

                if not cc_pairs:
                    print("❌ 没有找到任何连接器凭证对")
                    return []

                print(f"📋 找到 {len(cc_pairs)} 个连接器凭证对:")
                for cc_pair in cc_pairs:
                    connector = cc_pair.connector
                    credential = cc_pair.credential

                    # 获取最新的索引尝试
                    latest_attempt = get_latest_index_attempt(
                        connector_credential_pair_id=cc_pair.id,
                        db_session=db_session
                    )

                    status = "无索引记录"
                    if latest_attempt:
                        status = latest_attempt.status.value

                    print(f"  ID: {cc_pair.id}")
                    print(f"  连接器: {connector.name} ({connector.source.value})")
                    print(f"  凭证: {credential.credential_json.get('username', 'N/A')}")
                    print(f"  状态: {status}")
                    print(f"  最后更新: {cc_pair.last_successful_index_time or '从未'}")
                    print("-" * 40)

                return cc_pairs
        except Exception as e:
            print(f"❌ Python API查询失败: {e}")
            print("🔄 切换到SQL查询模式...")

    # 使用SQL查询
    query = """
    SELECT
        ccp.id,
        c.name as connector_name,
        c.source,
        c.disabled,
        ccp.last_successful_index_time,
        cr.credential_json->>'username' as username
    FROM connector_credential_pair ccp
    JOIN connector c ON ccp.connector_id = c.id
    JOIN credential cr ON ccp.credential_id = cr.id
    ORDER BY ccp.id;
    """

    run_sql_query(query, "连接器凭证对列表 (SQL查询)")
    return []

def get_cc_pair_status(cc_pair_id: int):
    """获取特定连接器凭证对的详细状态"""
    if IMPORTS_SUCCESSFUL:
        # 使用Python API
        try:
            with get_session_with_current_tenant() as db_session:
                cc_pairs = get_connector_credential_pairs(db_session)
                cc_pair = next((cp for cp in cc_pairs if cp.id == cc_pair_id), None)

                if not cc_pair:
                    print(f"❌ 未找到ID为 {cc_pair_id} 的连接器凭证对")
                    return None

                print(f"=== 连接器凭证对 {cc_pair_id} 详细状态 ===")
                print(f"连接器名称: {cc_pair.connector.name}")
                print(f"连接器类型: {cc_pair.connector.source.value}")
                print(f"连接器配置: {cc_pair.connector.connector_specific_config}")
                print(f"凭证用户: {cc_pair.credential.credential_json.get('username', 'N/A')}")
                print(f"是否启用: {'是' if not cc_pair.connector.disabled else '否'}")
                print(f"最后成功索引: {cc_pair.last_successful_index_time or '从未'}")

                # 获取最近的索引尝试
                latest_attempt = get_latest_index_attempt(
                    connector_credential_pair_id=cc_pair_id,
                    db_session=db_session
                )

                if latest_attempt:
                    print(f"最新索引尝试:")
                    print(f"  ID: {latest_attempt.id}")
                    print(f"  状态: {latest_attempt.status.value}")
                    print(f"  开始时间: {latest_attempt.time_started or '未开始'}")
                    print(f"  完成时间: {latest_attempt.time_finished or '未完成'}")
                    print(f"  错误信息: {latest_attempt.error_msg or '无'}")
                    print(f"  Celery任务ID: {latest_attempt.celery_task_id or '无'}")
                else:
                    print("无索引尝试记录")

                return cc_pair
        except Exception as e:
            print(f"❌ Python API查询失败: {e}")
            print("🔄 切换到SQL查询模式...")

    # 使用SQL查询
    query = f"""
    SELECT
        ccp.id,
        c.name as connector_name,
        c.source,
        c.disabled,
        c.connector_specific_config,
        cr.credential_json->>'username' as username,
        ccp.last_successful_index_time
    FROM connector_credential_pair ccp
    JOIN connector c ON ccp.connector_id = c.id
    JOIN credential cr ON ccp.credential_id = cr.id
    WHERE ccp.id = {cc_pair_id};
    """

    run_sql_query(query, f"连接器凭证对 {cc_pair_id} 详细状态")

    # 查询最新的索引尝试
    attempt_query = f"""
    SELECT
        id,
        status,
        time_started,
        time_finished,
        error_msg,
        celery_task_id
    FROM index_attempt
    WHERE connector_credential_pair_id = {cc_pair_id}
    ORDER BY time_created DESC
    LIMIT 1;
    """

    run_sql_query(attempt_query, f"连接器 {cc_pair_id} 最新索引尝试")
    return None

def manual_trigger_indexing(cc_pair_id: int, from_beginning: bool = False):
    """手动触发索引任务"""
    print(f"=== 手动触发索引任务 ===")
    print(f"连接器凭证对ID: {cc_pair_id}")
    print(f"从头开始: {'是' if from_beginning else '否'}")

    if not IMPORTS_SUCCESSFUL:
        print("❌ 无法触发索引任务，Python模块导入失败")
        print("💡 请使用以下方法手动触发:")
        print("1. 重启Celery服务: supervisorctl restart celery_primary celery_beat")
        print("2. 检查Celery状态: supervisorctl status")
        print("3. 查看日志: tail -f /var/log/celery_primary.log")
        return

    try:
        # 发送检查索引任务
        print("📤 发送 CHECK_FOR_INDEXING 任务...")
        result = celery_app.send_task(
            OnyxCeleryTask.CHECK_FOR_INDEXING,
            priority=OnyxCeleryPriority.HIGHEST,
            kwargs={"tenant_id": "public"}  # 默认租户
        )

        if result:
            print(f"✅ 任务已发送，任务ID: {result.id}")
            print("⏳ 等待任务执行...")

            # 等待一段时间让任务执行
            time.sleep(5)

            # 检查任务状态
            try:
                task_result = result.get(timeout=30)
                print(f"📊 任务执行结果: {task_result}")
            except Exception as e:
                print(f"⚠️ 获取任务结果超时或失败: {e}")
                print("💡 任务可能仍在后台执行，请检查日志")

        else:
            print("❌ 任务发送失败")

    except Exception as e:
        print(f"❌ 触发索引任务时出错: {e}")
        if logger:
            logger.exception("手动触发索引任务失败")

def check_celery_status():
    """检查Celery状态"""
    print("=== Celery状态检查 ===")

    if not IMPORTS_SUCCESSFUL:
        print("❌ 无法检查Celery状态，Python模块导入失败")
        print("💡 请使用以下命令手动检查:")
        print("1. supervisorctl status")
        print("2. celery -A onyx.background.celery.versioned_apps.primary inspect active")
        print("3. ps aux | grep celery")
        return

    try:
        # 检查Celery是否可用
        inspect = celery_app.control.inspect()

        # 获取活跃任务
        active_tasks = inspect.active()
        if active_tasks:
            print("🔄 活跃任务:")
            for worker, tasks in active_tasks.items():
                print(f"  Worker: {worker}")
                for task in tasks:
                    print(f"    任务: {task.get('name', 'Unknown')}")
                    print(f"    ID: {task.get('id', 'Unknown')}")
        else:
            print("📭 没有活跃任务")

        # 获取已注册的任务
        registered_tasks = inspect.registered()
        if registered_tasks:
            print("\n📋 已注册的任务:")
            for worker, tasks in registered_tasks.items():
                print(f"  Worker: {worker}")
                for task in tasks[:5]:  # 只显示前5个
                    print(f"    {task}")
                if len(tasks) > 5:
                    print(f"    ... 还有 {len(tasks) - 5} 个任务")

        # 获取统计信息
        stats = inspect.stats()
        if stats:
            print("\n📊 Worker统计:")
            for worker, stat in stats.items():
                print(f"  Worker: {worker}")
                print(f"    状态: {'在线' if stat else '离线'}")
                if stat:
                    print(f"    进程数: {stat.get('pool', {}).get('processes', 'Unknown')}")
                    print(f"    总任务数: {stat.get('total', 'Unknown')}")

    except Exception as e:
        print(f"❌ 检查Celery状态时出错: {e}")
        if logger:
            logger.exception("检查Celery状态失败")

def main():
    """主函数"""
    print("🚀 Onyx手动索引触发工具")
    print("=" * 50)

    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python manual_trigger_indexing.py env                     # 检查环境")
        print("  python manual_trigger_indexing.py list                    # 列出所有连接器")
        print("  python manual_trigger_indexing.py status <cc_pair_id>     # 查看连接器状态")
        print("  python manual_trigger_indexing.py trigger <cc_pair_id>    # 触发索引")
        print("  python manual_trigger_indexing.py celery                  # 检查Celery状态")
        print("  python manual_trigger_indexing.py sql                     # 显示SQL查询示例")
        print()
        print("💡 如果遇到import错误，请先运行 'env' 命令检查环境")
        return

    command = sys.argv[1].lower()

    try:
        if command == "env":
            check_environment()

        elif command == "list":
            list_connector_credential_pairs()

        elif command == "status":
            if len(sys.argv) < 3:
                print("❌ 请提供连接器凭证对ID")
                return
            cc_pair_id = int(sys.argv[2])
            get_cc_pair_status(cc_pair_id)

        elif command == "trigger":
            if len(sys.argv) < 3:
                print("❌ 请提供连接器凭证对ID")
                return
            cc_pair_id = int(sys.argv[2])
            from_beginning = len(sys.argv) > 3 and sys.argv[3].lower() == "full"
            manual_trigger_indexing(cc_pair_id, from_beginning)

        elif command == "celery":
            check_celery_status()

        elif command == "sql":
            show_sql_examples()

        else:
            print(f"❌ 未知命令: {command}")

    except ValueError as e:
        print(f"❌ 参数错误: {e}")
    except Exception as e:
        print(f"❌ 执行过程中出错: {e}")
        if logger:
            logger.exception("执行命令失败")

def show_sql_examples():
    """显示SQL查询示例"""
    print("=== SQL查询示例 ===")
    print()

    print("1. 查看所有连接器凭证对:")
    print("```sql")
    print("""SELECT
    ccp.id,
    c.name as connector_name,
    c.source,
    c.disabled,
    ccp.last_successful_index_time,
    cr.credential_json->>'username' as username
FROM connector_credential_pair ccp
JOIN connector c ON ccp.connector_id = c.id
JOIN credential cr ON ccp.credential_id = cr.id
ORDER BY ccp.id;""")
    print("```")
    print()

    print("2. 查看最近的索引尝试:")
    print("```sql")
    print("""SELECT
    ia.id,
    ia.connector_credential_pair_id,
    ia.status,
    ia.time_created,
    ia.time_started,
    ia.time_finished,
    ia.error_msg
FROM index_attempt ia
ORDER BY ia.time_created DESC
LIMIT 10;""")
    print("```")
    print()

    print("3. 查看正在进行的索引:")
    print("```sql")
    print("""SELECT
    ia.id,
    ia.connector_credential_pair_id,
    ia.status,
    ia.time_started,
    ia.celery_task_id
FROM index_attempt ia
WHERE ia.status IN ('NOT_STARTED', 'IN_PROGRESS')
ORDER BY ia.time_created DESC;""")
    print("```")
    print()

    print("4. 查看特定连接器的详细信息 (替换 <cc_pair_id>):")
    print("```sql")
    print("""SELECT
    ccp.id,
    c.name,
    c.source,
    c.connector_specific_config,
    cr.credential_json,
    ccp.last_successful_index_time
FROM connector_credential_pair ccp
JOIN connector c ON ccp.connector_id = c.id
JOIN credential cr ON ccp.credential_id = cr.id
WHERE ccp.id = <cc_pair_id>;""")
    print("```")
    print()

    print("💡 执行方法:")
    print("psql -h localhost -U postgres -d onyx -c \"<SQL查询>\"")
    print()
    print("或者进入psql交互模式:")
    print("psql -h localhost -U postgres -d onyx")

if __name__ == "__main__":
    main()
