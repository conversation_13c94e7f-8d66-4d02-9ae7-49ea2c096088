# Onyx连接器索引问题排查指南

## 问题描述
连接器显示为"active"状态，但仍处于"Initial Indexing"阶段，后台没有日志输出，索引任务未启动。

## 快速诊断步骤

### 1. 运行快速诊断脚本
```bash
# 在服务器上执行
cd /path/to/onyx
chmod +x backend/quick_diagnosis.sh
./backend/quick_diagnosis.sh
```

### 2. 检查Celery服务状态
```bash
# 检查Supervisor管理的Celery服务
supervisorctl status

# 如果服务未运行，重启服务
supervisorctl restart celery_primary
supervisorctl restart celery_beat
supervisorctl restart celery_docfetching
supervisorctl restart celery_docprocessing
```

### 3. 检查Docker服务
```bash
# 查看Docker Compose服务状态
docker compose ps

# 如果有服务未运行，重启
docker compose restart
```

### 4. 手动触发索引任务
```bash
# 列出所有连接器
python backend/manual_trigger_indexing.py list

# 查看特定连接器状态
python backend/manual_trigger_indexing.py status <cc_pair_id>

# 手动触发索引
python backend/manual_trigger_indexing.py trigger <cc_pair_id>

# 检查Celery状态
python backend/manual_trigger_indexing.py celery
```

## 常见问题和解决方案

### 1. Celery Worker未启动
**症状**: `supervisorctl status` 显示celery服务为FATAL或STOPPED状态

**解决方案**:
```bash
# 查看错误日志
supervisorctl tail celery_primary stderr

# 重启服务
supervisorctl restart celery_primary celery_beat

# 如果仍然失败，检查配置文件
cat /etc/supervisor/conf.d/supervisord.conf
```

### 2. Redis连接问题
**症状**: Redis ping失败或无法连接

**解决方案**:
```bash
# 检查Redis服务
systemctl status redis
docker compose ps redis

# 重启Redis
systemctl restart redis
# 或
docker compose restart redis

# 测试连接
redis-cli ping
```

### 3. PostgreSQL连接问题
**症状**: 数据库连接失败

**解决方案**:
```bash
# 检查PostgreSQL服务
systemctl status postgresql
docker compose ps postgres

# 测试连接
psql -h localhost -U postgres -d onyx -c "SELECT 1;"

# 检查连接器数据
psql -h localhost -U postgres -d onyx -c "SELECT id, name FROM connector;"
```

### 4. 环境变量配置问题
**症状**: 关键环境变量未设置

**解决方案**:
```bash
# 检查环境变量文件
cat .env

# 重新加载环境变量
source .env

# 重启相关服务
supervisorctl restart all
```

### 5. 任务队列阻塞
**症状**: 任务发送成功但不执行

**解决方案**:
```bash
# 清理Redis中的任务队列
redis-cli flushdb

# 重启所有Celery服务
supervisorctl restart celery_primary celery_beat celery_docfetching celery_docprocessing

# 手动触发检查任务
python -c "
from onyx.background.celery.celery_app import celery_app
from onyx.background.celery.tasks.shared import OnyxCeleryTask, OnyxCeleryPriority
result = celery_app.send_task(OnyxCeleryTask.CHECK_FOR_INDEXING, priority=OnyxCeleryPriority.HIGHEST, kwargs={'tenant_id': 'public'})
print(f'Task sent: {result.id}')
"
```

## 日志文件位置

### Celery日志
- `/var/log/celery_primary.log` - 主要worker日志
- `/var/log/celery_beat.log` - 调度器日志
- `/var/log/celery_docfetching.log` - 文档获取日志
- `/var/log/celery_docprocessing.log` - 文档处理日志

### 应用日志
- `/var/log/onyx_web_server.log` - Web服务器日志
- `/var/log/background_jobs.log` - 后台任务日志

### 系统日志
```bash
# 查看系统日志
journalctl -u supervisor
journalctl -u docker
journalctl -u redis
journalctl -u postgresql
```

## 监控命令

### 实时监控Celery任务
```bash
# 监控活跃任务
watch -n 5 "celery -A onyx.background.celery.versioned_apps.primary inspect active"

# 监控任务统计
celery -A onyx.background.celery.versioned_apps.primary inspect stats

# 监控队列状态
celery -A onyx.background.celery.versioned_apps.primary inspect reserved
```

### 实时监控日志
```bash
# 监控主要日志
tail -f /var/log/celery_primary.log

# 监控所有Celery日志
tail -f /var/log/celery_*.log

# 监控特定连接器的索引过程
grep "Email.*Processing" /var/log/celery_*.log | tail -20
```

## 数据库查询

### 检查连接器状态
```sql
-- 查看所有连接器凭证对
SELECT 
    ccp.id,
    c.name as connector_name,
    c.source,
    c.disabled,
    ccp.last_successful_index_time
FROM connector_credential_pair ccp
JOIN connector c ON ccp.connector_id = c.id;

-- 查看最近的索引尝试
SELECT 
    ia.id,
    ia.connector_credential_pair_id,
    ia.status,
    ia.time_created,
    ia.time_started,
    ia.time_finished,
    ia.error_msg
FROM index_attempt ia
ORDER BY ia.time_created DESC
LIMIT 10;

-- 查看正在进行的索引尝试
SELECT * FROM index_attempt 
WHERE status IN ('NOT_STARTED', 'IN_PROGRESS');
```

## 紧急恢复步骤

如果所有诊断都失败，按以下顺序执行完整重启：

```bash
# 1. 停止所有服务
supervisorctl stop all
docker compose down

# 2. 清理临时数据
redis-cli flushdb

# 3. 重启基础服务
docker compose up -d postgres redis

# 4. 等待服务启动
sleep 10

# 5. 重启应用服务
docker compose up -d

# 6. 重启Supervisor服务
supervisorctl start all

# 7. 验证服务状态
supervisorctl status
docker compose ps

# 8. 手动触发索引
python backend/manual_trigger_indexing.py trigger <cc_pair_id>
```

## 联系支持

如果问题仍然存在，请收集以下信息：
1. 快速诊断脚本的完整输出
2. 最近24小时的Celery日志
3. 数据库中连接器和索引尝试的状态
4. 系统资源使用情况

然后联系技术支持团队。
