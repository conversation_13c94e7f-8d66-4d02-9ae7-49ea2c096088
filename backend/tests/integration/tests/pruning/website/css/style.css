/*
Author URI: http://webthemez.com/
Note: 
Licence under Creative Commons Attribution 3.0 
Do not remove the back-link in this web template 
-------------------------------------------------------*/

@import url("http://fonts.googleapis.com/css?family=Noto+Serif:400,400italic,700|Open+Sans:400,600,700");
@import url("font-awesome.css");
@import url("animate.css");

body {
  font-family: "Open Sans", Arial, sans-serif;
  font-size: 14px;
  font-weight: 300;
  line-height: 1.6em;
  color: #656565;
}

a:active {
  outline: 0;
}

.clear {
  clear: both;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Open Sans", Arial, sans-serif;
  font-weight: 700;
  line-height: 1.1em;
  color: #333;
  margin-bottom: 20px;
}

.container {
  padding: 0 20px 0 20px;
  position: relative;
}

#wrapper {
  width: 100%;
  margin: 0;
  padding: 0;
}

.row,
.row-fluid {
  margin-bottom: 30px;
}

.row .row,
.row-fluid .row-fluid {
  margin-bottom: 30px;
}

.row.nomargin,
.row-fluid.nomargin {
  margin-bottom: 0;
}

img.img-polaroid {
  margin: 0 0 20px 0;
}
.img-box {
  max-width: 100%;
}
/*  Header
==================================== */

header .navbar {
  margin-bottom: 0;
}

.navbar-default {
  border: none;
}

.navbar-brand {
  color: #222;
  text-transform: uppercase;
  font-size: 24px;
  font-weight: 700;
  line-height: 1em;
  letter-spacing: -1px;
  margin-top: 13px;
  padding: 0 0 0 15px;
}
.navbar-default .navbar-brand {
  color: #61b331;
}

header .navbar-collapse ul.navbar-nav {
  float: right;
  margin-right: 0;
}

header .navbar-default {
  background-color: #ffffff;
}

header .nav li a:hover,
header .nav li a:focus,
header .nav li.active a,
header .nav li.active a:hover,
header .nav li a.dropdown-toggle:hover,
header .nav li a.dropdown-toggle:focus,
header .nav li.active ul.dropdown-menu li a:hover,
header .nav li.active ul.dropdown-menu li.active a {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

header .navbar-default .navbar-nav > .open > a,
header .navbar-default .navbar-nav > .open > a:hover,
header .navbar-default .navbar-nav > .open > a:focus {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

header .navbar {
  min-height: 70px;
  padding: 18px 0;
}

header .navbar-nav > li {
  padding-bottom: 12px;
  padding-top: 12px;
}

header .navbar-nav > li > a {
  padding-bottom: 6px;
  padding-top: 5px;
  margin-left: 2px;
  line-height: 30px;
  font-weight: 700;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.dropdown-menu li a:hover {
  color: #fff !important;
}

header .nav .caret {
  border-bottom-color: #f5f5f5;
  border-top-color: #f5f5f5;
}
.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus {
  background-color: #fff;
}
.navbar-default .navbar-nav > .open > a,
.navbar-default .navbar-nav > .open > a:hover,
.navbar-default .navbar-nav > .open > a:focus {
  background-color: #fff;
}

.dropdown-menu {
  box-shadow: none;
  border-radius: 0;
  border: none;
}

.dropdown-menu li:last-child {
  padding-bottom: 0 !important;
  margin-bottom: 0;
}

header .nav li .dropdown-menu {
  padding: 0;
}

header .nav li .dropdown-menu li a {
  line-height: 28px;
  padding: 3px 12px;
}
.item-thumbs img {
  margin-bottom: 15px;
}
.flex-control-paging li a.flex-active {
  background: #000;
  background: rgb(255, 255, 255);
  cursor: default;
}
.flex-control-paging li a {
  width: 30px;
  height: 11px;
  display: block;
  background: #666;
  background: rgba(0, 0, 0, 0.5);
  cursor: pointer;
  text-indent: -9999px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -o-border-radius: 20px;
  border-radius: 20px;
  box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
}
.panel-title > a {
  color: inherit;
  color: #fff;
}
.panel-group .panel-heading + .panel-collapse .panel-body {
  border-top: 1px solid #ddd;
  color: #fff;
  background-color: #9c9c9c;
}
/* --- menu --- */

header .navigation {
  float: right;
}

header ul.nav li {
  border: none;
  margin: 0;
}

header ul.nav li a {
  font-size: 12px;
  border: none;
  font-weight: 700;
  text-transform: uppercase;
}

header ul.nav li ul li a {
  font-size: 12px;
  border: none;
  font-weight: 300;
  text-transform: uppercase;
}

.navbar .nav > li > a {
  color: #848484;
  text-shadow: none;
  border: 1px solid rgba(255, 255, 255, 0) !important;
}

.navbar .nav a:hover {
  background: none;
  color: #14a085 !important;
}

.navbar .nav > .active > a,
.navbar .nav > .active > a:hover {
  background: none;
  font-weight: 700;
}

.navbar .nav > .active > a:active,
.navbar .nav > .active > a:focus {
  background: none;
  outline: 0;
  font-weight: 700;
}

.navbar .nav li .dropdown-menu {
  z-index: 2000;
}

header ul.nav li ul {
  margin-top: 1px;
}
header ul.nav li ul li ul {
  margin: 1px 0 0 1px;
}
.dropdown-menu .dropdown i {
  position: absolute;
  right: 0;
  margin-top: 3px;
  padding-left: 20px;
}

.navbar .nav > li > .dropdown-menu:before {
  display: inline-block;
  border-right: none;
  border-bottom: none;
  border-left: none;
  border-bottom-color: none;
  content: none;
}
.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus {
  color: #14a085;
}

ul.nav li.dropdown a {
  z-index: 1000;
  display: block;
}

select.selectmenu {
  display: none;
}
.pageTitle {
  color: #fff;
  margin: 30px 0 3px;
  display: inline-block;
}

#featured {
  width: 100%;
  background: #000;
  position: relative;
  margin: 0;
  padding: 0;
}

/*  Sliders
==================================== */
/* --- flexslider --- */

#featured .flexslider {
  padding: 0;
  background: #fff;
  position: relative;
  zoom: 1;
}
.flex-direction-nav .flex-prev {
  left: 0px;
}
.flex-direction-nav .flex-next {
  right: 0px;
}
.flex-caption {
  zoom: 0;
  color: #1c1d21;
  margin: 0 auto;
  padding: 1px;
  position: absolute;
  vertical-align: bottom;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.26);
  bottom: 5%;
  display: block;
  left: 0;
  right: 0;
}
.flex-caption h3 {
  color: #fff;
  letter-spacing: 1px;
  margin-bottom: 8px;
  text-transform: uppercase;
}
.flex-caption p {
  margin: 0 0 15px;
}
.skill-home {
  margin-bottom: 50px;
}
.c1 {
  border: #ed5441 1px solid;
  background: #ed5441;
}
.c2 {
  border: #d867b2 1px solid;
  background: #d867b2;
}
.c3 {
  border: #61b331 1px solid;
  background: #4bc567;
}
.c4 {
  border: #609cec 1px solid;
  background: #26aff0;
}
.skill-home .icons {
  padding: 33px 0 0 0;
  width: 100%;
  height: 178px;
  color: rgb(255, 255, 255);
  font-size: 42px;
  font-size: 76px;
  text-align: center;
  -ms-border-radius: 50%;
  -moz-border-radius: 50%;
  -webkit-border-radius: 50%;
  border-radius: 0;
  display: inline-table;
}
.skill-home h2 {
  padding-top: 20px;
  font-size: 36px;
  font-weight: 700;
}
.testimonial-solid {
  padding: 50px 0 60px 0;
  margin: 0 0 0 0;
  background: #efefef;
  text-align: center;
}
.testi-icon-area {
  text-align: center;
  position: absolute;
  top: -84px;
  margin: 0 auto;
  width: 100%;
  color: #000;
}
.testi-icon-area .quote {
  padding: 15px 0 0 0;
  margin: 0 0 0 0;
  background: #ffffff;
  text-align: center;
  color: #26aff0;
  display: inline-table;
  width: 70px;
  height: 70px;
  -ms-border-radius: 50%;
  -moz-border-radius: 50%;
  -webkit-border-radius: 50%;
  border-radius: 0;
  font-size: 42px;
  border: 1px solid #26aff0;
  display: none;
}

.testi-icon-area .carousel-inner {
  margin: 20px 0;
}
.carousel-indicators {
  bottom: -30px;
}
.team-member {
  text-align: center;
  background-color: #f9f9f9;
  padding-bottom: 15px;
}
.fancybox-title-inside-wrap {
  padding: 3px 30px 6px;
  background: #292929;
}

.item_introtext {
  background-color: rgba(254, 254, 255, 0.66);
  margin: 0 auto;
  display: inline-block;
  padding: 25px;
}
.item_introtext span {
  font-size: 20px;
  display: block;
  font-weight: bold;
}
.item_introtext strong {
  font-size: 50px;
  display: block;
  padding: 14px 0 30px;
}
.item_introtext p {
  font-size: 20px !important;
  color: #1c1d21;
  font-weight: bold;
}

.form-control {
  border-radius: 0;
}

/* Testimonial
----------------------------------*/
.testimonial-area {
  padding: 0 0 0 0;
  margin: 0;
  background: url(../img/low-poly01.jpg) fixed center center;
  background-size: cover;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -ms-background-size: cover;
  color: red;
}
.testimonial-solid p {
  color: #1f1f1f;
  font-size: 16px;
  line-height: 30px;
  font-style: italic;
}
section.callaction {
  background: #fff;
  padding: 50px 0 0 0;
}

/* Content
==================================== */

#content {
  position: relative;
  background: #fff;
  padding: 50px 0 0px 0;
}

#content img {
  max-width: 100%;
  height: auto;
}

.cta-text {
  text-align: center;
  margin-top: 10px;
}

.big-cta .cta {
  margin-top: 10px;
}

.box {
  width: 100%;
}
.box-gray {
  background: #f8f8f8;
  padding: 20px 20px 30px;
}
.box-gray h4,
.box-gray i {
  margin-bottom: 20px;
}
.box-bottom {
  padding: 20px 0;
  text-align: center;
}
.box-bottom a {
  color: #fff;
  font-weight: 700;
}
.box-bottom a:hover {
  color: #eee;
  text-decoration: none;
}

/* Bottom
==================================== */

#bottom {
  background: #fcfcfc;
  padding: 50px 0 0;
}
/* twitter */
#twitter-wrapper {
  text-align: center;
  width: 70%;
  margin: 0 auto;
}
#twitter em {
  font-style: normal;
  font-size: 13px;
}

#twitter em.twitterTime a {
  font-weight: 600;
}

#twitter ul {
  padding: 0;
  list-style: none;
}
#twitter ul li {
  font-size: 20px;
  line-height: 1.6em;
  font-weight: 300;
  margin-bottom: 20px;
  position: relative;
  word-break: break-word;
}

/* page headline
==================================== */

#inner-headline {
  background: #14a085;
  position: relative;
  margin: 0;
  padding: 0;
  color: #fefefe;
  /* margin: 15px; */
  border-top: 10px solid #11967c;
}

#inner-headline .inner-heading h2 {
  color: #fff;
  margin: 20px 0 0 0;
}

/* --- breadcrumbs --- */
#inner-headline ul.breadcrumb {
  margin: 30px 0 0;
  float: left;
}

#inner-headline ul.breadcrumb li {
  margin-bottom: 0;
  padding-bottom: 0;
}
#inner-headline ul.breadcrumb li {
  font-size: 13px;
  color: #fff;
}

#inner-headline ul.breadcrumb li i {
  color: #dedede;
}

#inner-headline ul.breadcrumb li a {
  color: #fff;
}

ul.breadcrumb li a:hover {
  text-decoration: none;
}

/* Forms
============================= */

/* --- contact form  ---- */
form#contactform input[type="text"] {
  width: 100%;
  border: 1px solid #f5f5f5;
  min-height: 40px;
  padding-left: 20px;
  font-size: 13px;
  padding-right: 20px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

form#contactform textarea {
  border: 1px solid #f5f5f5;
  width: 100%;
  padding-left: 20px;
  padding-top: 10px;
  font-size: 13px;
  padding-right: 20px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

form#contactform .validation {
  font-size: 11px;
}

#sendmessage {
  border: 1px solid #e6e6e6;
  background: #f6f6f6;
  display: none;
  text-align: center;
  padding: 15px 12px 15px 65px;
  margin: 10px 0;
  font-weight: 600;
  margin-bottom: 30px;
}

#sendmessage.show,
.show {
  display: block;
}

form#commentform input[type="text"] {
  width: 100%;
  min-height: 40px;
  padding-left: 20px;
  font-size: 13px;
  padding-right: 20px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-border-radius: 2px 2px 2px 2px;
  -moz-border-radius: 2px 2px 2px 2px;
  border-radius: 2px 2px 2px 2px;
}

form#commentform textarea {
  width: 100%;
  padding-left: 20px;
  padding-top: 10px;
  font-size: 13px;
  padding-right: 20px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-border-radius: 2px 2px 2px 2px;
  -moz-border-radius: 2px 2px 2px 2px;
  border-radius: 2px 2px 2px 2px;
}

/* --- search form --- */
.search {
  float: right;
  margin: 35px 0 0;
  padding-bottom: 0;
}

#inner-headline form.input-append {
  margin: 0;
  padding: 0;
}

/*  Portfolio
================================ */

.work-nav #filters {
  margin: 0;
  padding: 0;
  list-style: none;
}

.work-nav #filters li {
  margin: 0 10px 30px 0;
  padding: 0;
  float: left;
}

.work-nav #filters li a {
  color: #7f8289;
  font-size: 16px;
  display: block;
}

.work-nav #filters li a:hover {
}

.work-nav #filters li a.selected {
  color: #de5e60;
}

#thumbs {
  margin: 0;
  padding: 0;
}

#thumbs li {
  list-style-type: none;
}

.item-thumbs {
  position: relative;
  overflow: hidden;
  margin-bottom: 30px;
  cursor: pointer;
}

.item-thumbs a + img {
  width: 100%;
}

.item-thumbs .hover-wrap {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;

  opacity: 0;
  filter: alpha(opacity=0);

  -webkit-transition: all 450ms ease-out 0s;
  -moz-transition: all 450ms ease-out 0s;
  -o-transition: all 450ms ease-out 0s;
  transition: all 450ms ease-out 0s;

  -webkit-transform: rotateY(180deg) scale(0.5, 0.5);
  -moz-transform: rotateY(180deg) scale(0.5, 0.5);
  -ms-transform: rotateY(180deg) scale(0.5, 0.5);
  -o-transform: rotateY(180deg) scale(0.5, 0.5);
  transform: rotateY(180deg) scale(0.5, 0.5);
}

.item-thumbs:hover .hover-wrap,
.item-thumbs.active .hover-wrap {
  opacity: 1;
  filter: alpha(opacity=100);

  -webkit-transform: rotateY(0deg) scale(1, 1);
  -moz-transform: rotateY(0deg) scale(1, 1);
  -ms-transform: rotateY(0deg) scale(1, 1);
  -o-transform: rotateY(0deg) scale(1, 1);
  transform: rotateY(0deg) scale(1, 1);
}

.item-thumbs .hover-wrap .overlay-img {
  position: absolute;
  width: 90%;
  height: 91%;
  opacity: 0.5;
  filter: alpha(opacity=80);
  background: #14a085;
}

.item-thumbs .hover-wrap .overlay-img-thumb {
  position: absolute;
  border-radius: 60px;
  top: 50%;
  left: 45%;
  margin: -16px 0 0 -16px;
  color: #fff;
  font-size: 32px;
  line-height: 1em;
  opacity: 1;
  filter: alpha(opacity=100);
}

ul.portfolio-categ {
  margin: 10px 0 30px 0;
  padding: 0;
  float: left;
  list-style: none;
}

ul.portfolio-categ li {
  margin: 0;
  float: left;
  list-style: none;
  font-size: 13px;
  font-weight: 600;
  border: 1px solid #d5d5d5;
  margin-right: 15px;
}

ul.portfolio-categ li a {
  display: block;
  padding: 8px 20px;
  color: #14a085;
}
ul.portfolio-categ li.active {
  border: 1px solid #d7d8d6;

  background-color: #eaeaea;
}
ul.portfolio-categ li.active a:hover,
ul.portfolio-categ li a:hover,
ul.portfolio-categ li a:focus,
ul.portfolio-categ li a:active {
  text-decoration: none;
  outline: 0;
}
#accordion-alt3 .panel-heading h4 {
  font-size: 13px;
  line-height: 28px;
  color: #6b6b6b;
}
.panel .panel-heading h4 {
  font-weight: 400;
}
.panel-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 15px;
  color: inherit;
}
.panel-group .panel {
  margin-bottom: 0;
  border-radius: 2px;
}
.panel {
  margin-bottom: 18px;
  background-color: #b9b9b9;
  border: 1px solid transparent;
  border-radius: 2px;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}
#accordion-alt3 .panel-heading h4 a i {
  font-size: 13px;
  line-height: 18px;
  width: 18px;
  height: 18px;
  margin-right: 5px;
  color: #fff;
  text-align: center;
  border-radius: 50%;
  margin-left: 6px;
}
.progress.pb-sm {
  height: 6px !important;
}
.progress {
  box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.1);
}
.progress {
  overflow: hidden;
  height: 18px;
  margin-bottom: 18px;
  background-color: #f5f5f5;
  border-radius: 2px;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}
.progress .progress-bar.progress-bar-red {
  background: #ed5441;
}
.progress .progress-bar.progress-bar-green {
  background: #51d466;
}
.progress .progress-bar.progress-bar-lblue {
  background: #32c8de;
}
/* --- portfolio detail --- */
.top-wrapper {
  margin-bottom: 20px;
}
.info-blocks {
  margin-bottom: 15px;
}
.info-blocks i.icon-info-blocks {
  float: left;
  color: #318fcf;
  font-size: 30px;
  min-width: 50px;
  margin-top: 6px;
  text-align: center;
  background-color: #efefef;
  padding: 15px;
}
.info-blocks .info-blocks-in {
  padding: 0 10px;
  overflow: hidden;
}
.info-blocks .info-blocks-in h3 {
  color: #555;
  font-size: 20px;
  line-height: 28px;
  margin: 0px;
}
.info-blocks .info-blocks-in p {
  font-size: 12px;
}

blockquote {
  font-size: 16px;
  font-weight: 400;
  font-family: "Noto Serif", serif;
  font-style: italic;
  padding-left: 0;
  color: #a2a2a2;
  line-height: 1.6em;
  border: none;
}

blockquote cite {
  display: block;
  font-size: 12px;
  color: #666;
  margin-top: 10px;
}
blockquote cite:before {
  content: "\2014 \0020";
}
blockquote cite a,
blockquote cite a:visited,
blockquote cite a:visited {
  color: #555;
}

/* --- pullquotes --- */

.pullquote-left {
  display: block;
  color: #a2a2a2;
  font-family: "Noto Serif", serif;
  font-size: 14px;
  line-height: 1.6em;
  padding-left: 20px;
}

.pullquote-right {
  display: block;
  color: #a2a2a2;
  font-family: "Noto Serif", serif;
  font-size: 14px;
  line-height: 1.6em;
  padding-right: 20px;
}

/* --- button --- */
.btn {
  text-align: center;
  background: #318cca;
  color: #fff;
  border-radius: 0;
  padding: 10px 30px;
}
.btn-theme {
  color: #fff;
}
.btn-theme:hover {
  color: #eee;
}

/* --- list style --- */

ul.general {
  list-style: none;
  margin-left: 0;
}

ul.link-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

ul.link-list li {
  margin: 0;
  padding: 2px 0 2px 0;
  list-style: none;
}
footer {
  background: #14a085;
}
footer ul.link-list li a {
  color: #ffffff;
}
footer ul.link-list li a:hover {
  color: #e2e2e2;
}
/* --- Heading style --- */

h4.heading {
  font-weight: 700;
}

.heading {
  margin-bottom: 30px;
}

.heading {
  position: relative;
}

.widgetheading {
  width: 100%;

  padding: 0;
}

#bottom .widgetheading {
  position: relative;
  border-bottom: #e6e6e6 1px solid;
  padding-bottom: 9px;
}

aside .widgetheading {
  position: relative;
  border-bottom: #e9e9e9 1px solid;
  padding-bottom: 9px;
}

footer .widgetheading {
  position: relative;
}

footer .widget .social-network {
  position: relative;
}

#bottom .widget .widgetheading span,
aside .widget .widgetheading span,
footer .widget .widgetheading span {
  position: absolute;
  width: 60px;
  height: 1px;
  bottom: -1px;
  right: 0;
}
.box-area {
  border: 1px solid #f3f3f3;
  padding: 0 15px 12px;
  padding-top: 41px;
  margin-top: -42px;
  text-align: left;
  background-color: #f9f9f9;
  position: relative;
}
/* --- Map --- */
.map {
  position: relative;
  margin-top: -50px;
  margin-bottom: 40px;
}

.map iframe {
  width: 100%;
  height: 450px;
  border: none;
}

.map-grid iframe {
  width: 100%;
  height: 350px;
  border: none;
  margin: 0 0 -5px 0;
  padding: 0;
}

ul.team-detail {
  margin: -10px 0 0 0;
  padding: 0;
  list-style: none;
}

ul.team-detail li {
  border-bottom: 1px dotted #e9e9e9;
  margin: 0 0 15px 0;
  padding: 0 0 15px 0;
  list-style: none;
}

ul.team-detail li label {
  font-size: 13px;
}

ul.team-detail li h4,
ul.team-detail li label {
  margin-bottom: 0;
}

ul.team-detail li ul.social-network {
  border: none;
  margin: 0;
  padding: 0;
}

ul.team-detail li ul.social-network li {
  border: none;
  margin: 0;
}
ul.team-detail li ul.social-network li i {
  margin: 0;
}

.pricing-title {
  background: #fff;
  text-align: center;
  padding: 10px 0 10px 0;
}

.pricing-title h3 {
  font-weight: 600;
  margin-bottom: 0;
}

.pricing-offer {
  background: #fcfcfc;
  text-align: center;
  padding: 40px 0 40px 0;
  font-size: 18px;
  border-top: 1px solid #e6e6e6;
  border-bottom: 1px solid #e6e6e6;
}

.pricing-box.activeItem .pricing-offer {
  color: #fff;
}

.pricing-offer strong {
  font-size: 78px;
  line-height: 89px;
}

.pricing-offer sup {
  font-size: 28px;
}

.pricing-container {
  background: #fff;
  text-align: center;
  font-size: 14px;
}

.pricing-container strong {
  color: #353535;
}

.pricing-container ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.pricing-container ul li {
  border-bottom: 1px solid #f5f5f5;
  list-style: none;
  padding: 15px 0 15px 0;
  margin: 0 0 0 0;
  color: #222;
}

.pricing-action {
  margin: 0;
  background: #fcfcfc;
  text-align: center;
  padding: 20px 0 30px 0;
}

.pricing-wrapp {
  margin: 0 auto;
  width: 100%;
  background: #fd0000;
}
.pricing-box-item {
  border: 1px solid #f5f5f5;

  background: #f9f9f9;
  position: relative;
  margin: 0 0 20px 0;
  padding: 0;
  -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.03);
  -moz-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.03);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.03);
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.pricing-box-item .pricing-heading {
  text-align: center;
  padding: 0px 0 0px 0;
  display: block;
}
.pricing-box-item.activeItem .pricing-heading {
  text-align: center;
  padding: 0px 0 1px 0;
  border-bottom: none;
  display: block;
  color: #fff;
}
.pricing-box-item.activeItem .pricing-heading h3 {
}

.pricing-box-item .pricing-heading h3 strong {
  font-size: 20px;
  font-weight: 700;
  letter-spacing: -1px;
}
.pricing-box-item .pricing-heading h3 {
  font-size: 35px;
  font-weight: 300;
  letter-spacing: -1px;
}

.pricing-box-item .pricing-terms {
  text-align: center;
  display: block;
  overflow: hidden;
  padding: 11px 0 5px;
}

.pricing-box-item .pricing-terms h6 {
  font-style: italic;
  margin-top: 10px;
  color: #14a085;
  font-size: 22px;
  font-family: "Noto Serif", serif;
}

.pricing-box-item .icon .price-circled {
  margin: 10px 10px 10px 0;
  display: inline-block !important;
  text-align: center !important;
  color: #fff;
  width: 68px;
  height: 68px;
  padding: 12px;
  font-size: 16px;
  font-weight: 700;
  line-height: 68px;
  text-shadow: none;
  cursor: pointer;
  background-color: #888;
  border-radius: 64px;
  -moz-border-radius: 64px;
  -webkit-border-radius: 64px;
}

.pricing-box-item .pricing-action {
  margin: 0;
  text-align: center;
  padding: 30px 0 30px 0;
}

/* ===== Widgets ===== */

/* --- flickr --- */
.widget .flickr_badge {
  width: 100%;
}
.widget .flickr_badge img {
  margin: 0 9px 20px 0;
}

footer .widget .flickr_badge {
  width: 100%;
}
footer .widget .flickr_badge img {
  margin: 0 9px 20px 0;
}

.flickr_badge img {
  width: 50px;
  height: 50px;
  float: left;
  margin: 0 9px 20px 0;
}

/* --- Recent post widget --- */

.recent-post {
  margin: 20px 0 0 0;
  padding: 0;
  line-height: 18px;
}

.recent-post h5 a:hover {
  text-decoration: none;
}

.recent-post .text h5 a {
  color: #353535;
}

footer {
  padding: 50px 0 0 0;
  color: #f8f8f8;
}

footer a {
  color: #fff;
}

footer a:hover {
  color: #eee;
}

footer h1,
footer h2,
footer h3,
footer h4,
footer h5,
footer h6 {
  color: #fff;
}

footer address {
  line-height: 1.6em;
  color: #ffffff;
}

footer h5 a:hover,
footer a:hover {
  text-decoration: none;
}

ul.social-network {
  list-style: none;
  margin: 0;
}

ul.social-network li {
  display: inline;
  margin: 0 5px;
}

#sub-footer {
  text-shadow: none;
  color: #f5f5f5;
  padding: 0;
  padding-top: 30px;
  margin: 20px 0 0 0;
  background: #14a085;
}

#sub-footer p {
  margin: 0;
  padding: 0;
}

#sub-footer span {
  color: #f5f5f5;
}

.copyright {
  text-align: left;
  font-size: 12px;
}

#sub-footer ul.social-network {
  float: right;
}

/* scroll to top */
.scrollup {
  position: fixed;
  width: 32px;
  height: 32px;
  bottom: 0px;
  right: 20px;
  background: #222;
}

a.scrollup {
  outline: 0;
  text-align: center;
}

a.scrollup:hover,
a.scrollup:active,
a.scrollup:focus {
  opacity: 1;
  text-decoration: none;
}
a.scrollup i {
  margin-top: 10px;
  color: #fff;
}
a.scrollup i:hover {
  text-decoration: none;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.aligncenter {
  text-align: center;
}

.aligncenter span {
  margin-left: 0;
}

.floatright {
  float: right;
}

.floatleft {
  float: left;
}

.floatnone {
  float: none;
}

.aligncenter {
  text-align: center;
}

img.pull-left,
.align-left {
  float: left;
  margin: 0 15px 15px 0;
}

.widget img.pull-left {
  float: left;
  margin: 0 15px 15px 0;
}

img.pull-right,
.align-right {
  float: right;
  margin: 0 0 15px 15px;
}

article img.pull-left,
article .align-left {
  float: left;
  margin: 5px 15px 15px 0;
}

article img.pull-right,
article .align-right {
  float: right;
  margin: 5px 0 15px 15px;
}
============================= */ .clear-marginbot {
  margin-bottom: 0;
}

.marginbot10 {
  margin-bottom: 10px;
}
.marginbot20 {
  margin-bottom: 20px;
}
.marginbot30 {
  margin-bottom: 30px;
}
.marginbot40 {
  margin-bottom: 40px;
}

.clear-margintop {
  margin-top: 0;
}

.margintop10 {
  margin-top: 10px;
}

.margintop20 {
  margin-top: 20px;
}

.margintop30 {
  margin-top: 30px;
}

.margintop40 {
  margin-top: 40px;
}

/*  Media queries 
============================= */

@media (min-width: 768px) and (max-width: 979px) {
  a.detail {
    background: none;
    width: 100%;
  }

  footer .widget form input#appendedInputButton {
    display: block;
    width: 91%;
    -webkit-border-radius: 4px 4px 4px 4px;
    -moz-border-radius: 4px 4px 4px 4px;
    border-radius: 4px 4px 4px 4px;
  }

  footer .widget form .input-append .btn {
    display: block;
    width: 100%;
    padding-right: 0;
    padding-left: 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    margin-top: 10px;
  }

  ul.related-folio li {
    width: 156px;
    margin: 0 20px 0 0;
  }
}

@media (max-width: 767px) {
  body {
    padding-right: 0;
    padding-left: 0;
  }
  .navbar-brand {
    margin-top: 10px;
    border-bottom: none;
  }
  .navbar-header {
    margin-top: 20px;
    border-bottom: none;
  }

  .navbar-nav {
    border-top: none;
    float: none;
    width: 100%;
  }
  .navbar .nav > .active > a,
  .navbar .nav > .active > a:hover {
    background: none;
    font-weight: 700;
    color: #26aff0;
  }
  header .navbar-nav > li {
    padding-bottom: 0px;
    padding-top: 2px;
  }
  header .nav li .dropdown-menu {
    margin-top: 0;
  }

  .dropdown-menu {
    position: absolute;
    top: 0;
    left: 40px;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    font-size: 13px;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #f5f5f5;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 0;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  }

  .navbar-collapse.collapse {
    border: none;
    overflow: hidden;
  }

  .box {
    border-bottom: 1px solid #e9e9e9;
    padding-bottom: 20px;
  }

  #featured .flexslider .slide-caption {
    width: 90%;
    padding: 2%;
    position: absolute;
    left: 0;
    bottom: -40px;
  }

  #inner-headline .breadcrumb {
    float: left;
    clear: both;
    width: 100%;
  }

  .breadcrumb > li {
    font-size: 13px;
  }

  ul.portfolio li article a i.icon-48 {
    width: 20px;
    height: 20px;
    font-size: 16px;
    line-height: 20px;
  }

  .left-sidebar {
    border-right: none;
    padding: 0 0 0 0;
    border-bottom: 1px dotted #e6e6e6;
    padding-bottom: 10px;
    margin-bottom: 40px;
  }

  .right-sidebar {
    margin-top: 30px;
    border-left: none;
    padding: 0 0 0 0;
  }

  footer .col-lg-1,
  footer .col-lg-2,
  footer .col-lg-3,
  footer .col-lg-4,
  footer .col-lg-5,
  footer .col-lg-6,
  footer .col-lg-7,
  footer .col-lg-8,
  footer .col-lg-9,
  footer .col-lg-10,
  footer .col-lg-11,
  footer .col-lg-12 {
    margin-bottom: 20px;
  }

  #sub-footer ul.social-network {
    float: left;
  }

  [class*="span"] {
    margin-bottom: 20px;
  }
}

@media (max-width: 480px) {
  .bottom-article a.pull-right {
    float: left;
    margin-top: 20px;
  }

  .search {
    float: left;
  }

  .flexslider .flex-caption {
    display: none;
  }

  .cta-text {
    margin: 0 auto;
    text-align: center;
  }

  ul.portfolio li article a i {
    width: 20px;
    height: 20px;
    font-size: 14px;
  }
}

.box-area:before {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
  background-color: red;
  content: "";
  position: absolute;
  top: 7px;
  left: -1px;
  width: 100%;
  height: 23px;
  background: #f9f9f9;
  -moz-transform: skewY(-3deg);
  -o-transform: skewY(-3deg);
  -ms-transform: skewY(-3deg);
  -webkit-transform: skewY(-3deg);
  transform: skewY(11deg);
  background-size: cover;
}
.box-area:after {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
  background-color: red;
  content: "";
  position: absolute;
  top: 7px;
  left: 1px;
  width: 100%;
  height: 22px;
  background: #f9f9f9;
  -moz-transform: skewY(-3deg);
  -o-transform: skewY(-3deg);
  -ms-transform: skewY(-3deg);
  -webkit-transform: skewY(-3deg);
  transform: skewY(-11deg);
  background-size: cover;
}
.box-area h3 {
  margin-top: -16px;
  z-index: 12;
  position: relative;
}
.courses {
  padding: 50px 0;
}
.carousel-indicators li {
  display: inline-block;
  border: 1px solid #929292;
}
.textbox {
  background-color: #efefef;
  padding: 4px 25px;
}
.textbox h3 {
  margin: 0;
  padding: 22px 0 14px;
  font-size: 18px;
}
