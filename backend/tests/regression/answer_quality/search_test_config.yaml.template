# Copy this to search_test_config.yaml and fill in the values to run the eval pipeline
# Don't forget to also update the .env_eval file with the correct values

# Directory where test results will be saved
output_folder: "~/onyx_test_results"

# Path to the zip file containing sample documents
zipped_documents_file: "~/sampledocs.zip"

# Path to the YAML file containing sample questions
questions_file: "~/sample_questions.yaml"

# Git commit SHA to use (null means use current code as is)
commit_sha: null

# Whether to launch a web UI for the test
launch_web_ui: false

# Only retrieve documents, not LLM response
only_retrieve_docs: false

# Whether to use a cloud GPU for processing
use_cloud_gpu: false

# IP address of the model server (placeholder)
model_server_ip: "PUT_PUBLIC_CLOUD_IP_HERE"

# Port of the model server (placeholder)
model_server_port: "PUT_PUBLIC_CLOUD_PORT_HERE"

# Name for existing testing env (empty string uses default ports)
environment_name: ""

# Limit on number of tests to run (null means no limit)
limit: null

# LLM configuration
llm:
  # Name of the LLM
  name: "default_test_llm"
  
  # Provider of the LLM (e.g., OpenAI)
  provider: "openai"
  
  # API key
  api_key: "PUT_API_KEY_HERE"
  
  # Default model name to use
  default_model_name: "gpt-4o"
  
  # List of model names to use for testing
  model_names: ["gpt-4o"]
