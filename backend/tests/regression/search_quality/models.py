from pydantic import BaseModel

from onyx.configs.constants import DocumentSource
from onyx.context.search.models import SavedSearchDoc


class GroundTruth(BaseModel):
    doc_source: DocumentSource
    doc_link: str


class TestQuery(BaseModel):
    question: str
    ground_truth: list[GroundTruth] = []
    ground_truth_response: str | None = None
    categories: list[str] = []

    # autogenerated
    ground_truth_docids: list[str] = []


class EvalConfig(BaseModel):
    max_search_results: int
    max_answer_context: int
    num_workers: int  # 0 = unlimited
    max_request_rate: int  # 0 = unlimited
    request_timeout: int
    api_url: str
    search_only: bool


class OneshotQAResult(BaseModel):
    time_taken: float
    top_documents: list[SavedSearchDoc]
    answer: str | None


class RetrievedDocument(BaseModel):
    document_id: str
    chunk_id: int
    content: str


class AnalysisSummary(BaseModel):
    question: str
    categories: list[str]
    found: bool
    rank: int | None
    total_results: int
    ground_truth_count: int
    response_relevancy: float | None = None
    faithfulness: float | None = None
    factual_correctness: float | None = None
    answer: str | None = None
    retrieved: list[RetrievedDocument] = []
    time_taken: float


class SearchMetrics(BaseModel):
    total_queries: int
    found_count: int

    # for found results
    best_rank: int
    worst_rank: int
    average_rank: float
    top_k_accuracy: dict[int, float]


class AnswerMetrics(BaseModel):
    response_relevancy: float
    faithfulness: float
    factual_correctness: float

    # only for metric computation
    n_response_relevancy: int
    n_faithfulness: int
    n_factual_correctness: int


class CombinedMetrics(SearchMetrics, AnswerMetrics):
    average_time_taken: float
