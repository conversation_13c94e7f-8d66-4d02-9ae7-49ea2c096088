#!/usr/bin/env python3
"""
测试脚本：验证multipart邮件解析错误修复
用于测试"Unexpected end of multipart data"错误的修复效果
"""

import email
import logging
from typing import Optional
from email.message import Message

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def test_multipart_parsing_with_enhanced_error_handling(raw_email: bytes, email_id: str = "test") -> Optional[Message]:
    """
    测试增强的multipart邮件解析错误处理
    模拟修复后的_fetch_email函数中的解析逻辑
    """
    try:
        if not isinstance(raw_email, bytes):
            logger.error(
                f"Email {email_id}: Message data should be bytes; instead got {type(raw_email)=}"
            )
            return None
        
        # Check basic data integrity before parsing
        if len(raw_email) == 0:
            logger.warn(f"Email {email_id}: Empty email data received")
            return None
        
        # Log email size for monitoring
        email_size = len(raw_email)
        logger.debug(f"Email {email_id}: Processing email of size {email_size} bytes")
        
        return email.message_from_bytes(raw_email)
    except (email.errors.MessageParseError, UnicodeDecodeError, ValueError, TypeError) as e:
        # Handle specific parsing errors including multipart data issues
        error_type = type(e).__name__
        if "multipart" in str(e).lower() or "unexpected end" in str(e).lower():
            logger.error(
                f"Email {email_id}: Multipart parsing error ({error_type}): {e}. "
                f"Email size: {len(raw_email) if isinstance(raw_email, bytes) else 'unknown'} bytes"
            )
        else:
            logger.error(f"Email {email_id}: Email parsing error ({error_type}): {e}")
        return None
    except Exception as e:
        logger.error(f"Email {email_id}: Unexpected error parsing email: {e}")
        return None

def create_corrupted_multipart_email() -> bytes:
    """创建一个损坏的multipart邮件数据用于测试"""
    # 创建一个会触发"Unexpected end of multipart data"错误的邮件
    # 这种情况通常发生在multipart数据被截断时
    corrupted_email = b"""From: <EMAIL>
To: <EMAIL>
Subject: Test Multipart Email
Content-Type: multipart/mixed; boundary="boundary123"

--boundary123
Content-Type: text/plain

This is the first part.

--boundary123
Content-Type: application/octet-stream
Content-Transfer-Encoding: base64
Content-Disposition: attachment; filename="test.txt"

VGhpcyBpcyBhIHRlc3QgZmlsZSBjb250ZW50IGJ1dCB0aGUgZW1haWwgaXMgdHJ1bmNhdGVkIGhlcmUgd2l0aG91dCBwcm9wZXIgZW5kaW5nIGJvdW5kYXJ5IGFuZCB0aGlzIGNhdXNlcyBtdWx0aXBhcnQgcGFyc2luZyBlcnJvcnMgYmVjYXVzZSB0aGUgZGF0YSBpcyBpbmNvbXBsZXRlIGFuZCBjYW5ub3QgYmUgcHJvcGVybHkgcGFyc2VkIGJ5IHRoZSBlbWFpbCBsaWJyYXJ5IHdoaWNoIGV4cGVjdHMgYSBwcm9wZXIgbXVsdGlwYXJ0IGVuZGluZyBib3VuZGFyeSBidXQgaW5zdGVhZCBnZXRzIGEgdHJ1bmNhdGVkIGRhdGEgc3RyZWFtIHRoYXQgY2F1c2VzIGEgVHlwZUVycm9yIHdpdGggdGhlIG1lc3NhZ2UgVW5leHBlY3RlZCBlbmQgb2YgbXVsdGlwYXJ0IGRhdGE="""

    return corrupted_email

def create_normal_multipart_email() -> bytes:
    """创建一个正常的multipart邮件数据用于测试"""
    normal_email = b"""From: <EMAIL>
To: <EMAIL>
Subject: Test Normal Multipart Email
Content-Type: multipart/mixed; boundary="boundary123"

--boundary123
Content-Type: text/plain

This is the first part.

--boundary123
Content-Type: text/html

<html><body>This is HTML part</body></html>

--boundary123--
"""
    
    return normal_email

def test_type_error_handling():
    """直接测试TypeError处理"""
    print("测试5：直接测试TypeError处理")

    # 模拟一个会抛出TypeError的情况
    class MockEmailParser:
        @staticmethod
        def message_from_bytes(data):
            raise TypeError("Unexpected end of multipart data")

    # 临时替换email.message_from_bytes
    original_parser = email.message_from_bytes
    email.message_from_bytes = MockEmailParser.message_from_bytes

    try:
        test_data = b"some test data"
        result = test_multipart_parsing_with_enhanced_error_handling(test_data, "type_error_test")
        if result is None:
            print("✓ TypeError被正确捕获和处理")
        else:
            print("✗ TypeError处理失败")
    finally:
        # 恢复原始解析器
        email.message_from_bytes = original_parser
    print()

def run_tests():
    """运行测试用例"""
    print("=== 测试multipart邮件解析错误修复 ===\n")

    # 测试1：正常的multipart邮件
    print("测试1：正常的multipart邮件")
    normal_email = create_normal_multipart_email()
    result = test_multipart_parsing_with_enhanced_error_handling(normal_email, "normal_test")
    if result:
        print("✓ 正常邮件解析成功")
        print(f"  主题: {result.get('Subject')}")
        print(f"  内容类型: {result.get_content_type()}")
    else:
        print("✗ 正常邮件解析失败")
    print()

    # 测试2：损坏的multipart邮件
    print("测试2：损坏的multipart邮件（模拟'Unexpected end of multipart data'错误）")
    corrupted_email = create_corrupted_multipart_email()
    result = test_multipart_parsing_with_enhanced_error_handling(corrupted_email, "corrupted_test")
    if result is None:
        print("✓ 损坏邮件被正确处理，返回None而不是抛出异常")
    else:
        print("✗ 损坏邮件处理异常")
        print(f"  实际解析成功，主题: {result.get('Subject') if result else 'N/A'}")
    print()

    # 测试3：空邮件数据
    print("测试3：空邮件数据")
    empty_email = b""
    result = test_multipart_parsing_with_enhanced_error_handling(empty_email, "empty_test")
    if result is None:
        print("✓ 空邮件被正确处理")
    else:
        print("✗ 空邮件处理异常")
    print()

    # 测试4：非bytes类型数据
    print("测试4：非bytes类型数据")
    invalid_data = "not bytes data"
    result = test_multipart_parsing_with_enhanced_error_handling(invalid_data, "invalid_test")
    if result is None:
        print("✓ 非bytes数据被正确处理")
    else:
        print("✗ 非bytes数据处理异常")
    print()

    # 测试5：直接测试TypeError
    test_type_error_handling()

    print("=== 测试完成 ===")

if __name__ == "__main__":
    run_tests()
