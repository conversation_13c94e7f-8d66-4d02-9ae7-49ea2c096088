#!/bin/bash

# 简单的数据库查询脚本
# 用于快速查看Onyx连接器状态

echo "🔍 Onyx数据库查询工具"
echo "===================="

# 数据库连接参数
DB_HOST="localhost"
DB_USER="postgres"
DB_NAME="onyx"

# 检查psql是否可用
if ! command -v psql &> /dev/null; then
    echo "❌ psql命令不可用，请安装PostgreSQL客户端"
    exit 1
fi

# 测试数据库连接
echo "🔗 测试数据库连接..."
if psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT 1;" &> /dev/null; then
    echo "✅ 数据库连接成功"
else
    echo "❌ 数据库连接失败"
    echo "💡 请检查："
    echo "  1. PostgreSQL服务是否运行"
    echo "  2. 用户名和密码是否正确"
    echo "  3. 数据库名称是否正确"
    exit 1
fi

echo ""

# 函数：执行查询
run_query() {
    local description="$1"
    local query="$2"
    
    echo "=== $description ==="
    echo "🔍 执行查询..."
    
    if psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "$query" 2>/dev/null; then
        echo "✅ 查询成功"
    else
        echo "❌ 查询失败"
    fi
    echo ""
}

# 如果提供了参数，执行特定查询
if [ $# -gt 0 ]; then
    case "$1" in
        "list"|"l")
            run_query "连接器凭证对列表" "
            SELECT 
                ccp.id as \"ID\",
                c.name as \"连接器名称\",
                c.source as \"类型\",
                CASE WHEN c.disabled THEN '已禁用' ELSE '已启用' END as \"状态\",
                cr.credential_json->>'username' as \"用户名\"
            FROM connector_credential_pair ccp
            JOIN connector c ON ccp.connector_id = c.id
            JOIN credential cr ON ccp.credential_id = cr.id
            ORDER BY ccp.id;"
            ;;
        "attempts"|"a")
            run_query "最近的索引尝试" "
            SELECT 
                ia.id as \"尝试ID\",
                ia.connector_credential_pair_id as \"连接器ID\",
                ia.status as \"状态\",
                ia.time_created as \"创建时间\"
            FROM index_attempt ia
            ORDER BY ia.time_created DESC
            LIMIT 10;"
            ;;
        "active"|"ac")
            run_query "正在进行的索引" "
            SELECT 
                ia.id as \"尝试ID\",
                ia.connector_credential_pair_id as \"连接器ID\",
                c.name as \"连接器名称\",
                ia.status as \"状态\",
                ia.time_started as \"开始时间\"
            FROM index_attempt ia
            JOIN connector_credential_pair ccp ON ia.connector_credential_pair_id = ccp.id
            JOIN connector c ON ccp.connector_id = c.id
            WHERE ia.status IN ('NOT_STARTED', 'IN_PROGRESS')
            ORDER BY ia.time_created DESC;"
            ;;
        "failed"|"f")
            run_query "失败的索引尝试" "
            SELECT 
                ia.id as \"尝试ID\",
                ia.connector_credential_pair_id as \"连接器ID\",
                c.name as \"连接器名称\",
                ia.time_created as \"时间\",
                LEFT(ia.error_msg, 100) as \"错误信息\"
            FROM index_attempt ia
            JOIN connector_credential_pair ccp ON ia.connector_credential_pair_id = ccp.id
            JOIN connector c ON ccp.connector_id = c.id
            WHERE ia.status = 'FAILED'
            ORDER BY ia.time_created DESC
            LIMIT 10;"
            ;;
        "status")
            if [ -z "$2" ]; then
                echo "❌ 请提供连接器ID"
                echo "用法: $0 status <connector_id>"
                exit 1
            fi
            
            run_query "连接器 $2 详细状态" "
            SELECT 
                ccp.id as \"连接器ID\",
                c.name as \"名称\",
                c.source as \"类型\",
                CASE WHEN c.disabled THEN '已禁用' ELSE '已启用' END as \"状态\",
                cr.credential_json->>'username' as \"用户名\",
                ccp.last_successful_index_time as \"最后成功索引\"
            FROM connector_credential_pair ccp
            JOIN connector c ON ccp.connector_id = c.id
            JOIN credential cr ON ccp.credential_id = cr.id
            WHERE ccp.id = $2;"
            
            run_query "连接器 $2 最近索引尝试" "
            SELECT 
                ia.id as \"尝试ID\",
                ia.status as \"状态\",
                ia.time_created as \"创建时间\",
                ia.time_started as \"开始时间\",
                ia.time_finished as \"完成时间\",
                ia.error_msg as \"错误信息\"
            FROM index_attempt ia
            WHERE ia.connector_credential_pair_id = $2
            ORDER BY ia.time_created DESC
            LIMIT 5;"
            ;;
        "help"|"h"|*)
            echo "用法: $0 <命令> [参数]"
            echo ""
            echo "可用命令:"
            echo "  list, l          - 列出所有连接器凭证对"
            echo "  attempts, a      - 显示最近的索引尝试"
            echo "  active, ac       - 显示正在进行的索引"
            echo "  failed, f        - 显示失败的索引尝试"
            echo "  status <id>      - 显示特定连接器的详细状态"
            echo "  help, h          - 显示此帮助信息"
            echo ""
            echo "示例:"
            echo "  $0 list          # 列出所有连接器"
            echo "  $0 status 1      # 查看连接器ID为1的状态"
            echo "  $0 active        # 查看正在进行的索引"
            ;;
    esac
else
    # 默认显示概览信息
    echo "📊 系统概览"
    echo "----------"
    
    run_query "连接器总数" "
    SELECT COUNT(*) as \"连接器总数\" FROM connector_credential_pair;"
    
    run_query "索引尝试统计" "
    SELECT 
        status as \"状态\",
        COUNT(*) as \"数量\"
    FROM index_attempt 
    GROUP BY status 
    ORDER BY COUNT(*) DESC;"
    
    echo "💡 使用 '$0 help' 查看更多命令"
fi
