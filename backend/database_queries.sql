-- Onyx数据库查询脚本
-- 用于查找连接器凭证对列表和相关信息

-- ============================================
-- 1. 查看所有连接器凭证对
-- ============================================
SELECT 
    ccp.id as "连接器ID",
    c.name as "连接器名称",
    c.source as "连接器类型",
    CASE 
        WHEN c.disabled THEN '已禁用'
        ELSE '已启用'
    END as "状态",
    cr.credential_json->>'username' as "用户名",
    ccp.last_successful_index_time as "最后成功索引时间"
FROM connector_credential_pair ccp
JOIN connector c ON ccp.connector_id = c.id
JOIN credential cr ON ccp.credential_id = cr.id
ORDER BY ccp.id;

-- ============================================
-- 2. 查看最近的索引尝试
-- ============================================
SELECT 
    ia.id as "索引尝试ID",
    ia.connector_credential_pair_id as "连接器ID",
    ia.status as "状态",
    ia.time_created as "创建时间",
    ia.time_started as "开始时间",
    ia.time_finished as "完成时间",
    CASE 
        WHEN ia.error_msg IS NOT NULL THEN LEFT(ia.error_msg, 100) || '...'
        ELSE '无错误'
    END as "错误信息"
FROM index_attempt ia
ORDER BY ia.time_created DESC
LIMIT 10;

-- ============================================
-- 3. 查看正在进行的索引任务
-- ============================================
SELECT 
    ia.id as "索引尝试ID",
    ia.connector_credential_pair_id as "连接器ID",
    c.name as "连接器名称",
    ia.status as "状态",
    ia.time_started as "开始时间",
    ia.celery_task_id as "Celery任务ID"
FROM index_attempt ia
JOIN connector_credential_pair ccp ON ia.connector_credential_pair_id = ccp.id
JOIN connector c ON ccp.connector_id = c.id
WHERE ia.status IN ('NOT_STARTED', 'IN_PROGRESS')
ORDER BY ia.time_created DESC;

-- ============================================
-- 4. 查看连接器配置详情
-- ============================================
SELECT 
    ccp.id as "连接器ID",
    c.name as "连接器名称",
    c.source as "类型",
    c.connector_specific_config as "连接器配置",
    cr.credential_json as "凭证配置"
FROM connector_credential_pair ccp
JOIN connector c ON ccp.connector_id = c.id
JOIN credential cr ON ccp.credential_id = cr.id
ORDER BY ccp.id;

-- ============================================
-- 5. 查看索引统计信息
-- ============================================
SELECT 
    ccp.id as "连接器ID",
    c.name as "连接器名称",
    COUNT(ia.id) as "总索引尝试次数",
    COUNT(CASE WHEN ia.status = 'SUCCESS' THEN 1 END) as "成功次数",
    COUNT(CASE WHEN ia.status = 'FAILED' THEN 1 END) as "失败次数",
    COUNT(CASE WHEN ia.status IN ('NOT_STARTED', 'IN_PROGRESS') THEN 1 END) as "进行中次数",
    MAX(ia.time_created) as "最近尝试时间"
FROM connector_credential_pair ccp
JOIN connector c ON ccp.connector_id = c.id
LEFT JOIN index_attempt ia ON ccp.id = ia.connector_credential_pair_id
GROUP BY ccp.id, c.name
ORDER BY ccp.id;

-- ============================================
-- 6. 查看失败的索引尝试及错误信息
-- ============================================
SELECT 
    ia.id as "索引尝试ID",
    ia.connector_credential_pair_id as "连接器ID",
    c.name as "连接器名称",
    ia.time_created as "创建时间",
    ia.error_msg as "错误信息"
FROM index_attempt ia
JOIN connector_credential_pair ccp ON ia.connector_credential_pair_id = ccp.id
JOIN connector c ON ccp.connector_id = c.id
WHERE ia.status = 'FAILED'
ORDER BY ia.time_created DESC
LIMIT 20;

-- ============================================
-- 7. 查看特定连接器的所有索引历史
-- 使用方法：将 <connector_id> 替换为实际的连接器ID
-- ============================================
/*
SELECT 
    ia.id as "索引尝试ID",
    ia.status as "状态",
    ia.time_created as "创建时间",
    ia.time_started as "开始时间",
    ia.time_finished as "完成时间",
    EXTRACT(EPOCH FROM (ia.time_finished - ia.time_started))/60 as "耗时(分钟)",
    ia.error_msg as "错误信息"
FROM index_attempt ia
WHERE ia.connector_credential_pair_id = <connector_id>
ORDER BY ia.time_created DESC;
*/

-- ============================================
-- 8. 查看系统中的所有表（用于调试）
-- ============================================
SELECT 
    table_name as "表名",
    table_type as "类型"
FROM information_schema.tables 
WHERE table_schema = 'public'
ORDER BY table_name;

-- ============================================
-- 使用说明
-- ============================================
/*
执行这些查询的方法：

1. 连接到数据库：
   psql -h localhost -U postgres -d onyx

2. 执行单个查询：
   psql -h localhost -U postgres -d onyx -c "SELECT * FROM connector_credential_pair;"

3. 执行文件中的查询：
   psql -h localhost -U postgres -d onyx -f database_queries.sql

4. 查看特定连接器（替换1为实际ID）：
   psql -h localhost -U postgres -d onyx -c "SELECT * FROM connector_credential_pair WHERE id = 1;"

常用的连接器状态：
- NOT_STARTED: 未开始
- IN_PROGRESS: 进行中  
- SUCCESS: 成功
- FAILED: 失败
- CANCELED: 已取消

如果遇到权限问题，可能需要：
1. 检查PostgreSQL服务是否运行
2. 确认用户名和密码
3. 检查数据库名称是否正确
*/
