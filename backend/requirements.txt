# Onyx 后端完整依赖包列表
# 基于实际安装测试验证的完整依赖
# 最后更新: 2025-01-09
# 验证状态: ✅ 所有286个包验证成功

# ===== 核心框架 =====
fastapi==0.115.12
fastapi-limiter==0.1.6
fastapi-users==14.0.1
fastapi-users-db-sqlalchemy==5.0.0
prometheus-fastapi-instrumentator==7.1.0
pydantic==2.11.3
pydantic-ai==0.4.7
pydantic-ai-slim==0.4.7
pydantic-evals==0.4.7
pydantic-graph==0.4.7
pydantic-settings==2.10.1
pydantic_core==2.33.1
sse-starlette==3.0.0
starlette==0.46.2
uvicorn==0.21.1

# ===== 数据库和ORM =====
alembic==1.10.4
asyncpg==0.30.0
psycopg2-binary==2.9.10
redis==5.0.8
SQLAlchemy==2.0.41

# ===== 异步任务队列 =====
amqp==5.3.1
billiard==4.2.1
celery==5.5.1
kombu==5.5.4
vine==5.1.0

# ===== AI和机器学习 =====
anthropic==0.59.0
cohere==5.16.1
huggingface-hub==0.34.1
langchain==0.3.23
langchain-community==0.3.21
langchain-core==0.3.51
langchain-openai==0.2.9
langchain-text-splitters==0.3.8
openai==1.75.0
sentence-transformers==5.1.0
tiktoken==0.11.0
torch==2.8.0
transformers==4.55.0

# ===== 文档处理 =====
filetype==1.2.0
openpyxl==3.1.5
puremagic==1.30
pypdf==5.9.0
python-docx==1.2.0
python-pptx==1.0.2
unstructured==0.18.11
unstructured-client==0.42.2
xlsxwriter==3.2.5

# ===== 网页处理 =====
beautifulsoup4==4.12.3
courlan==1.3.2
html5lib==1.1
htmldate==1.9.3
jusText==3.0.2
lxml==5.3.0
lxml_html_clean==0.4.2
trafilatura==2.0.0

# ===== HTTP客户端 =====
aiohttp==3.11.16
httpx==0.28.1
httpx-oauth==0.16.1
httpx-sse==0.4.0
requests==2.32.3
requests-file==2.1.0
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
types-requests==2.32.4.20250611
urllib3==2.4.0

# ===== 云服务集成 =====
aioboto3==14.0.0
boto3==1.36.23
boto3-stubs==1.40.6
google-api-core==2.25.1
google-api-python-client==2.86.0
google-auth==2.40.3
google-auth-httplib2==0.2.0
google-cloud-aiplatform==1.58.0
google-cloud-bigquery==3.35.1
google-cloud-core==2.4.3
google-cloud-resource-manager==1.14.2
google-cloud-storage==2.19.0
google-crc32c==1.7.1
google-genai==1.27.0
google-resumable-media==2.7.2
grpc-google-iam-v1==0.14.2
mypy-boto3-s3==1.40.0

# ===== 第三方服务连接器 =====
asana==5.2.0
atlassian-python-api==3.41.16
dropbox==12.0.2
hubspot-api-client==12.0.0
jira==3.10.5
Office365-REST-Python-Client==2.6.2
pyairtable==3.1.1
PyGithub==2.7.0
python-gitlab==6.2.0
simple-salesforce==1.12.6
stripe==12.4.0
zulip==0.9.0

# ===== 认证和安全 =====
argon2-cffi==23.1.0
argon2-cffi-bindings==25.1.0
bcrypt==4.3.0
cryptography==45.0.6
msal==1.33.0
oauthlib==3.3.1
passlib==1.7.4
pycryptodome==3.23.0
PyJWT==2.10.1

# ===== 监控和日志 =====
prometheus_client==0.22.1
sentry-sdk==2.34.1

# ===== 邮件服务 =====
sendgrid==6.12.4

# ===== 数据处理和分析 =====
datasets==4.0.0
evaluate==0.4.5
nltk==3.9.1
numpy==2.3.2
pandas==2.3.1
pyarrow==21.0.0
scikit-learn==1.7.1
scipy==1.16.1

# ===== 开发和测试 =====
coverage==7.10.0
pytest==8.4.1
pytest-asyncio==1.1.0
pytest-cov==6.2.1
pytest-mock==3.14.1

# ===== 工具库 =====
chardet==5.2.0
dateparser==1.2.2
emoji==2.14.1
inflection==0.5.1
jsonref==1.1.0
langdetect==1.0.9
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
retry==0.9.2
setuptools==80.9.0
timeago==1.0.16

# ===== 其他依赖 =====
accelerate==1.10.0
ag-ui-protocol==0.1.8
aiobotocore==2.20.0
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aioitertools==0.12.0
aiosignal==1.4.0
annotated-types==0.7.0
anyio==4.9.0
argcomplete==3.6.2
attrs==25.3.0
babel==2.17.0
backoff==2.2.1
botocore==1.36.23
botocore-stubs==1.38.46
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.2.1
click-didyoumean==0.3.1
click-plugins==*******
click-repl==0.3.0
colorama==0.4.6
dataclasses-json==0.6.7
decorator==5.2.1
defusedxml==0.7.1
Deprecated==1.2.18
dill==0.3.8
distro==1.9.0
dnspython==2.7.0
docstring_parser==0.17.0
ecdsa==0.19.1
email_validator==2.2.0
et_xmlfile==2.0.0
eval_type_backport==0.2.2
fastavro==1.11.1
filelock==3.15.4
firecrawl-py==1.16.0
frozenlist==1.7.0
fsspec==2025.3.0
googleapis-common-protos==1.70.0
greenlet==3.2.3
griffe==1.8.0
groq==0.30.0
grpcio==1.74.0
grpcio-status==1.62.3
h11==0.16.0
httpcore==1.0.9
httplib2==0.22.0
idna==3.10
IMAPClient==3.0.1
importlib_metadata==8.7.0
iniconfig==2.1.0
isodate==0.7.2
Jinja2==3.1.6
jiter==0.10.0
jmespath==1.0.1
joblib==1.5.1
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
langgraph==0.2.72
langgraph-checkpoint==2.1.1
langgraph-sdk==0.1.74
langsmith==0.3.45
litellm==1.72.2
logfire-api==4.0.0
makefun==1.16.0
Mako==1.3.10
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
mcp==1.12.2
mdurl==0.1.2
mistralai==1.9.3
more-itertools==10.7.0
mpmath==1.3.0
multidict==6.6.3
multiprocess==0.70.16
mypy_extensions==1.1.0
nest-asyncio==1.6.0
networkx==3.5
olefile==0.47
opentelemetry-api==1.35.0
orjson==3.11.1
ormsgpack==1.10.0
packaging==24.2
pillow==11.3.0
pip==25.2
platformdirs==4.3.8
pluggy==1.6.0
ply==3.11
prompt_toolkit==3.0.51
propcache==0.3.2
proto-plus==1.26.1
protobuf==4.25.8
psutil==7.0.0
pwdlib==0.2.1
py==1.11.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
pygame==2.6.1
Pygments==2.19.2
PyMySQL==1.1.1
PyNaCl==1.5.0
pyparsing==3.2.3
python-http-client==3.3.7
python-iso639==2025.2.18
python-magic==0.4.27
python-multipart==0.0.20
python-oxmsg==0.0.2
pytz==2025.2
pywin32==311
PyYAML==6.0.2
RapidFuzz==3.13.0
referencing==0.36.2
regex==2025.7.34
rich==14.1.0
rpds-py==0.26.0
rsa==4.9.1
s3transfer==0.11.3
safetensors==0.6.2
setfit==1.1.3
shapely==2.1.1
six==1.17.0
slack_sdk==3.36.0
sniffio==1.3.1
soupsieve==2.7
stone==3.3.1
sympy==1.14.0
tenacity==8.5.0
threadpoolctl==3.6.0
tld==0.13.1
tokenizers==0.21.2
tqdm==4.67.1
types-awscrt==0.27.5
types-s3transfer==0.13.0
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata==2025.2
tzlocal==5.3.1
uritemplate==4.2.0
wcwidth==0.2.13
webencodings==0.5.1
websockets==15.0.1
Werkzeug==3.1.3
wrapt==1.17.2
xxhash==3.5.0
yarl==1.20.1
zeep==4.3.1
zipp==3.23.0
zstandard==0.23.0
