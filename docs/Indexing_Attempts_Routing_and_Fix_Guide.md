# Onyx Indexing Attempts 端到端路由与基于 docker-compose.main.yml 的修复指南

> 适用对象：使用 `docker-compose.main.yml` 在服务器部署 Onyx 的环境。
> 目标：点击前端【Manage → Re-index】后，确保后台能创建 Indexing Attempts 并在 UI 展示。

---

## 1. 端到端访问路由（从前端到数据库）

1) 前端页面与触发
- 页面：`/admin/connector/[ccPairId]`
- 操作：右上角【Manage】→【Re-index】（打开 ReIndexModal）
- 代码调用链：
  - ReIndexModal → `triggerIndexing(fromBeginning, connectorId, credentialId, ccPairId)`
  - `triggerIndexing` → `runConnector(connectorId, [credentialId], fromBeginning)`
  - `runConnector` 发起：`POST /api/manage/admin/connector/run-once`

2) 后端 API 处理（km-api-server）
- 路由：`POST /manage/admin/connector/run-once`
- 行为：
  - `mark_ccpair_with_indexing_trigger(cc_pair_id, IndexingMode.UPDATE/REINDEX)`
  - 立即发送 Celery 任务：`OnyxCeleryTask.CHECK_FOR_INDEXING`（`kwargs={"tenant_id": <当前租户>}`）

3) Celery 调度与索引执行（km-background）
- 入口任务：`CHECK_FOR_INDEXING`
- 行为：
  - 判断哪些 cc_pair 需要索引
  - 创建“文档抓取”任务（docfetching）
  - 由 `IndexingCoordination.try_create_index_attempt(...)` 在数据库 `index_attempt` 表创建一条记录

4) 前端展示 Indexing Attempts
- 路由：`GET /manage/admin/cc-pair/{cc_pair_id}/index-attempts`
- 前端组件：`IndexingAttemptsTable` 渲染最近的尝试记录

> 只要 `CHECK_FOR_INDEXING → docfetching → try_create_index_attempt` 跑通，就能在 UI 看到新记录。

---

## 2. 您当前 docker-compose.main.yml 的关键点

- background 镜像来自 `docker/Dockerfile.worker`，其 CMD 仅启动单一 Celery worker：
  - `celery -A onyx.background.celery_app worker ...`
- Onyx 推荐：多 worker + celery beat（supervisor 管理），包括 primary/light/heavy/docfetching/docprocessing/beat。
- 若仅有单一 worker、且缺少 beat 与专用队列，`CHECK_FOR_INDEXING` 链路可能不完整，导致无 Indexing Attempts。

---

## 3. 修复三选一

### 方案A：临时热修复（不改镜像与 Compose）
在容器里直接手动启动 beat 与专用 worker，立即验证流水是否出现。

1) 进入 background 容器
```
docker exec -it km-background bash
```

2) 启动 Celery Beat（新增一个后台进程）
```
nohup celery -A onyx.background.celery.versioned_apps.beat beat --loglevel=INFO > /app/log/celery_beat_hotfix.log 2>&1 &
```

3) 启动 primary worker（关键：消费 CHECK_FOR_INDEXING 入口任务）
```
nohup celery -A onyx.background.celery.versioned_apps.primary worker --loglevel=INFO --hostname=primary@%n -Q celery > /app/log/celery_worker_primary_hotfix.log 2>&1 &
```

4) 启动 docfetching 专用 worker（新增后台进程）
```
nohup celery -A onyx.background.celery.versioned_apps.docfetching worker --loglevel=INFO --hostname=docfetching@%n -Q connector_doc_fetching > /app/log/celery_worker_docfetching_hotfix.log 2>&1 &
```

5) 启动 docprocessing 专用 worker（建议，保障后续处理）
```
nohup celery -A onyx.background.celery.apps.docprocessing worker --loglevel=INFO --hostname=docprocessing@%n -Q docprocessing > /app/log/celery_worker_docprocessing_hotfix.log 2>&1 &
```

6) 触发一次 Re-index 并观察日志
- 前端在 `/admin/connector/{ccPairId}` 点击【Manage】→【Re-index】
- 或在容器里：
```
cd /app
python manual_trigger_indexing.py trigger <cc_pair_id>
```
- 观察日志：
```
docker logs -n 300 -f km-background | egrep -i \
  "check_for_indexing|docfetching|index_attempt|Will index cc_pair_id|error|exception"
```

6) 刷新前端页面“Indexing Attempts”，应出现最新记录。

---

### 方案B：长期稳定修复（推荐，使用 supervisor 拉起全套 Celery 进程）
保持您的构建命令不变，仅在 Compose 覆盖 command。

1) 修改 docker-compose.main.yml → services.background 下新增：
```
command: ["supervisord", "-c", "/app/supervisord.conf"]
```

2) 重建并重启 background
```
nohup docker compose -f docker-compose.main.yml build --no-cache \
  > build_$(date +%Y%m%d%H%M).log 2>&1 &

docker compose -f docker-compose.main.yml up -d background
```

3) 观察日志（应看到多个 worker 与 beat 同时运行）
```
docker logs -n 200 -f km-background
```

---

### 方案C：容器内一次性脚本（无需改镜像/Compose）
直接在容器中运行项目提供的脚本，起多 worker + beat：

```
docker exec -it km-background bash -lc "cd /app && python ./scripts/dev_run_background_jobs.py"
```

此脚本会启动 primary / light / heavy / docprocessing / user_files_indexing / monitoring / kg_processing / docfetching / beat。

> 注意：方案A/C 更快捷，适合验证；方案B 最稳妥，适合长期运行。

---

## 4. 针对指定账号的核验步骤（示例）

假设这三位账号的 cc_pair_id 分别是 A、B、C：

1) 触发索引
```
docker exec -it km-api-server bash -lc "cd /app && python manual_trigger_indexing.py trigger A"
docker exec -it km-api-server bash -lc "cd /app && python manual_trigger_indexing.py trigger B"
docker exec -it km-api-server bash -lc "cd /app && python manual_trigger_indexing.py trigger C"
```

2) 后台日志观察
```
docker logs -n 500 -f km-background | egrep -i \
  "check_for_indexing|docfetching|index_attempt|Will index cc_pair_id|try_create_index_attempt|error|exception"
```

3) 数据库校验（如需要）
```
psql -h ********** -p 5432 -U km_user -d knowledge-manage <<'SQL'
SELECT id, connector_credential_pair_id, status, time_created, time_started, time_finished, error_msg
FROM index_attempt ORDER BY time_created DESC LIMIT 20;

SELECT id, connector_credential_pair_id, status, celery_task_id
FROM index_attempt WHERE status IN ('NOT_STARTED','IN_PROGRESS')
ORDER BY time_created DESC;
SQL
```

4) 前端刷新“Indexing Attempts”页面查看最新记录

---

## 5. 如何查询 cc_pair_id（3种方式）

- UI 方式：
  - 打开连接器详情页 `/admin/connector/[ccPairId]`，URL 上的数字就是 cc_pair_id。

- API 方式（在浏览器或 curl）
  - 获取所有连接器与凭证对：`GET /manage/admin/connector`、或在本页相关接口中获取 cc_pair 列表（需管理员权限）。

- SQL 方式（在 PostgreSQL 中）
```
-- 查看全部连接器凭证对，便于按账号/名称过滤
SELECT
  ccp.id AS cc_pair_id,
  c.name AS connector_name,
  c.source,
  cr.credential_json,
  ccp.last_successful_index_time
FROM connector_credential_pair ccp
JOIN connector c ON ccp.connector_id = c.id
JOIN credential cr ON ccp.credential_id = cr.id
ORDER BY ccp.id DESC;
```
> 提示：`credential_json` 中通常包含邮箱或用户名（如 <EMAIL>、cmsr_chinamobile、<EMAIL>），据此定位 cc_pair_id。

---

## 6. 环境一致性自查（.env.main 已具备但请核对）
- DATABASE_URL/REDIS_URL/CELERY_BROKER_URL/CELERY_RESULT_BACKEND 指向同一套 Redis/PG
- VESPA_HOST=km-vespa（容器名能解析）
- S3_ENDPOINT_URL 指向 MinIO
- INTERNAL_URL=http://km-api-server:8080

---

## 7. 常见问题与定位
- 缺少 beat / 专用 worker → 增加 beat 和 docfetching/docprocessing（见方案A/B/C）
- 连接器初始化失败（权限/配置）→ 查看 background 日志：`validate_connector_settings` 相关错误
- 已有进行中的 attempt（锁定）→ SQL 查询进行中的 attempt，等待完成或排障
- Redis/DB 连接问题 → 确认 .env.main 与容器网络连通
- Vespa 未健康 → km-vespa 健康检查并查看 background 启动阶段的 `wait_for_vespa_or_shutdown` 日志

---

## 8. 建议的最终状态
- 使用方案B（supervisor）或方案C（脚本）保证：
  - celery_beat 与多个队列的 worker 持续运行
  - 通过前端 Re-index 或 manual_trigger 脚本均能触发索引
  - Indexing Attempts 能稳定出现并更新

---

如需我为您的三个账号生成一键核验命令（包含实际 cc_pair_id），请将它们的 cc_pair_id 提供给我，或授权我在数据库中按邮箱/用户名筛选后直接回传结果。

---

## 9. 快速查询 cc_pair_id（按邮箱/用户名模糊匹配）

在 PostgreSQL 中，您可以用以下 SQL 直接按邮箱/用户名过滤，从而获得对应的 cc_pair_id：

```
-- 请按需替换 ILIKE 条件中的关键字
SELECT
  ccp.id AS cc_pair_id,
  c.name AS connector_name,
  c.source,
  cr.credential_json,
  ccp.last_successful_index_time
FROM connector_credential_pair ccp
JOIN connector c ON ccp.connector_id = c.id
JOIN credential cr ON ccp.credential_id = cr.id
WHERE
  cr.credential_json ILIKE '%<EMAIL>%'
  OR cr.credential_json ILIKE '%cmsr_chinamobile%'
  OR cr.credential_json ILIKE '%<EMAIL>%'
ORDER BY ccp.id DESC;
```

也可以封装为一次性执行（Linux）：

```
psql -h ********** -p 5432 -U km_user -d knowledge-manage <<'SQL'
SELECT
  ccp.id AS cc_pair_id,
  c.name AS connector_name,
  c.source,
  cr.credential_json,
  ccp.last_successful_index_time
FROM connector_credential_pair ccp
JOIN connector c ON ccp.connector_id = c.id
JOIN credential cr ON ccp.credential_id = cr.id
WHERE
  cr.credential_json ILIKE '%<EMAIL>%'
  OR cr.credential_json ILIKE '%cmsr_chinamobile%'
  OR cr.credential_json ILIKE '%<EMAIL>%'
ORDER BY ccp.id DESC;
SQL
```

---

## 10. 针对已知 cc_pair_id 的一键核验（示例：cc_pair_id=11）

1) 触发索引
```
docker exec -it km-api-server bash -lc "cd /app && python manual_trigger_indexing.py trigger 11"
```

2) 实时观察后台日志（检查是否出现调度与创建 Attempt 的关键行）
```
docker logs -n 500 -f km-background | egrep -i \
  "check_for_indexing|Will index cc_pair_id|docfetching|try_create_index_attempt|index_attempt|error|exception"
```

3) SQL 确认（最近 10 条 Index Attempts）
```
psql -h ********** -p 5432 -U km_user -d knowledge-manage <<'SQL'
SELECT id, connector_credential_pair_id, status, time_created, time_started, time_finished, error_msg
FROM index_attempt WHERE connector_credential_pair_id=11
ORDER BY time_created DESC LIMIT 10;
SQL
```

4) 前端刷新 `/admin/connector/11` 查看 Indexing Attempts 是否出现/更新

---

## 【备用方案】Compose 覆盖 command 为 supervisord（最小化 diff）

说明：保持您的构建命令不变，仅在 docker-compose.main.yml 中对 background 服务做最小化覆盖，让其用 supervisor 启动“多 worker + beat”。

1) 建议的 YAML 片段（请合并到 services.background 内）：

```yaml
services:
  background:
    # 原有镜像、环境、依赖保持不变，仅覆盖 command：
    command: ["supervisord", "-c", "/app/supervisord.conf"]
```

2) 重建并重启 background

```bash
nohup docker compose -f docker-compose.main.yml build --no-cache \
  > build_$(date +%Y%m%d%H%M).log 2>&1 &

docker compose -f docker-compose.main.yml up -d background

docker logs -n 200 -f km-background
```

3) 预期：日志里能看到 celery_beat 与多个 celery_worker_* 同时启动；随后按第10节对 cc_pair_id=11 进行一键核验。

