services:
  api_server:
    image: onyxdotapp/onyx-backend:${IMAGE_TAG:-latest}
    build:
      context: ../../backend
      dockerfile: Dockerfile
    command: >
      /bin/sh -c "
      alembic upgrade head &&
      echo \"Starting Onyx Api Server\" &&
      uvicorn onyx.main:app --host 0.0.0.0 --port 8080"
    depends_on:
      - relational_db
      - index
      - cache
      - minio
      - inference_model_server
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - AUTH_TYPE=${AUTH_TYPE:-oidc}
      - POSTGRES_HOST=relational_db
      - VESPA_HOST=index
      - REDIS_HOST=cache
      - MODEL_SERVER_HOST=${MODEL_SERVER_HOST:-inference_model_server}
      - USE_IAM_AUTH=${USE_IAM_AUTH}
      - AWS_REGION_NAME=${AWS_REGION_NAME-}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID-}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY-}
      # MinIO configuration
      - S3_ENDPOINT_URL=${S3_ENDPOINT_URL:-http://minio:9000}
      - S3_AWS_ACCESS_KEY_ID=${S3_AWS_ACCESS_KEY_ID:-minioadmin}
      - S3_AWS_SECRET_ACCESS_KEY=${S3_AWS_SECRET_ACCESS_KEY:-minioadmin}
    # Uncomment the line below to use if IAM_AUTH is true and you are using iam auth for postgres
    # volumes:
    #   - ./bundle.pem:/app/bundle.pem:ro
    extra_hosts:
      - "host.docker.internal:host-gateway"
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"
    volumes:
      - api_server_logs:/var/log

  background:
    image: onyxdotapp/onyx-backend:${IMAGE_TAG:-latest}
    build:
      context: ../../backend
      dockerfile: Dockerfile
    command: >
      /bin/sh -c "
      if [ -f /etc/ssl/certs/custom-ca.crt ]; then
        update-ca-certificates;
      fi &&
      /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf"
    depends_on:
      - relational_db
      - index
      - cache
      - inference_model_server
      - indexing_model_server
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - AUTH_TYPE=${AUTH_TYPE:-oidc}
      - POSTGRES_HOST=relational_db
      - VESPA_HOST=index
      - REDIS_HOST=cache
      - MODEL_SERVER_HOST=${MODEL_SERVER_HOST:-inference_model_server}
      - INDEXING_MODEL_SERVER_HOST=${INDEXING_MODEL_SERVER_HOST:-indexing_model_server}
      - USE_IAM_AUTH=${USE_IAM_AUTH}
      - AWS_REGION_NAME=${AWS_REGION_NAME-}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID-}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY-}
      # MinIO configuration
      - S3_ENDPOINT_URL=${S3_ENDPOINT_URL:-http://minio:9000}
      - S3_AWS_ACCESS_KEY_ID=${S3_AWS_ACCESS_KEY_ID:-minioadmin}
      - S3_AWS_SECRET_ACCESS_KEY=${S3_AWS_SECRET_ACCESS_KEY:-minioadmin}
    # Uncomment the line below to use if IAM_AUTH is true and you are using iam auth for postgres
    # volumes:
    #   - ./bundle.pem:/app/bundle.pem:ro
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - background_logs:/var/log
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"
    # Uncomment the following lines if you need to include a custom CA certificate
    # This section enables the use of a custom CA certificate
    # If present, the custom CA certificate is mounted as a volume
    # The container checks for its existence and updates the system's CA certificates
    # This allows for secure communication with services using custom SSL certificates
    # volumes:
    #   # Maps to the CA_CERT_PATH environment variable in the Dockerfile
    #   - ${CA_CERT_PATH:-./custom-ca.crt}:/etc/ssl/certs/custom-ca.crt:ro

  web_server:
    image: onyxdotapp/onyx-web-server:${IMAGE_TAG:-latest}
    build:
      context: ../../web
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_DISABLE_STREAMING=${NEXT_PUBLIC_DISABLE_STREAMING:-false}
        - NEXT_PUBLIC_NEW_CHAT_DIRECTS_TO_SAME_PERSONA=${NEXT_PUBLIC_NEW_CHAT_DIRECTS_TO_SAME_PERSONA:-false}
        - NEXT_PUBLIC_POSITIVE_PREDEFINED_FEEDBACK_OPTIONS=${NEXT_PUBLIC_POSITIVE_PREDEFINED_FEEDBACK_OPTIONS:-}
        - NEXT_PUBLIC_NEGATIVE_PREDEFINED_FEEDBACK_OPTIONS=${NEXT_PUBLIC_NEGATIVE_PREDEFINED_FEEDBACK_OPTIONS:-}
        - NEXT_PUBLIC_DISABLE_LOGOUT=${NEXT_PUBLIC_DISABLE_LOGOUT:-}
        - NEXT_PUBLIC_THEME=${NEXT_PUBLIC_THEME:-}
        - NEXT_PUBLIC_FORGOT_PASSWORD_ENABLED=${NEXT_PUBLIC_FORGOT_PASSWORD_ENABLED:-}
    depends_on:
      - api_server
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - INTERNAL_URL=http://api_server:8080
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"

  relational_db:
    image: postgres:15.2-alpine
    shm_size: 1g
    command: -c 'max_connections=250'
    restart: unless-stopped
    # POSTGRES_USER and POSTGRES_PASSWORD should be set in .env file
    env_file:
      - .env
    volumes:
      - db_volume:/var/lib/postgresql/data
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"

  inference_model_server:
    image: onyxdotapp/onyx-model-server:${IMAGE_TAG:-latest}
    build:
      context: ../../backend
      dockerfile: Dockerfile.model_server
    command: >
      /bin/sh -c "if [ \"${DISABLE_MODEL_SERVER:-false}\" = \"True\" ]; then
        echo 'Skipping service...';
        exit 0;
      else
        exec uvicorn model_server.main:app --host 0.0.0.0 --port 9000;
      fi"
    restart: unless-stopped
    environment:
      - MIN_THREADS_ML_MODELS=${MIN_THREADS_ML_MODELS:-}
      # Set to debug to get more fine-grained logs
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      # Not necessary, this is just to reduce download time during startup
      - model_cache_huggingface:/root/.cache/huggingface/
      # optional, only for debugging purposes
      - inference_model_server_logs:/var/log
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"

  indexing_model_server:
    image: onyxdotapp/onyx-model-server:${IMAGE_TAG:-latest}
    build:
      context: ../../backend
      dockerfile: Dockerfile.model_server
    command: >
      /bin/sh -c "if [ \"${DISABLE_MODEL_SERVER:-false}\" = \"True\" ]; then
        echo 'Skipping service...';
        exit 0;
      else
        exec uvicorn model_server.main:app --host 0.0.0.0 --port 9000;
      fi"
    restart: unless-stopped
    environment:
      - MIN_THREADS_ML_MODELS=${MIN_THREADS_ML_MODELS:-}
      - INDEXING_ONLY=True
      # Set to debug to get more fine-grained logs
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - VESPA_SEARCHER_THREADS=${VESPA_SEARCHER_THREADS:-1}
    volumes:
      # Not necessary, this is just to reduce download time during startup
      - indexing_huggingface_model_cache:/root/.cache/huggingface/
      # optional, only for debugging purposes
      - indexing_model_server_logs:/var/log
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"

  # This container name cannot have an underscore in it due to Vespa expectations of the URL
  index:
    image: vespaengine/vespa:8.526.15
    restart: unless-stopped
    environment:
      - VESPA_SKIP_UPGRADE_CHECK=true
    ports:
      - "19071:19071"
      - "8081:8081"
    volumes:
      - vespa_volume:/opt/vespa/var
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"

  nginx:
    image: nginx:1.23.4-alpine
    restart: unless-stopped
    # nginx will immediately crash with `nginx: [emerg] host not found in upstream`
    # if api_server / web_server are not up
    depends_on:
      - api_server
      - web_server
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../data/nginx:/etc/nginx/conf.d
      - ../data/certbot/conf:/etc/letsencrypt
      - ../data/certbot/www:/var/www/certbot
    # sleep a little bit to allow the web_server / api_server to start up.
    # Without this we've seen issues where nginx shows no error logs but
    # does not recieve any traffic
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"
    # The specified script waits for the api_server to start up.
    # Without this we've seen issues where nginx shows no error logs but
    # does not recieve any traffic
    # NOTE: we have to use dos2unix to remove Carriage Return chars from the file
    # in order to make this work on both Unix-like systems and windows
    command: >
      /bin/sh -c "dos2unix /etc/nginx/conf.d/run-nginx.sh 
      && /etc/nginx/conf.d/run-nginx.sh app.conf.template"
    env_file:
      - .env.nginx

  # follows https://pentacent.medium.com/nginx-and-lets-encrypt-with-docker-in-less-than-5-minutes-b4b8a60d3a71
  certbot:
    image: certbot/certbot
    restart: unless-stopped
    volumes:
      - ../data/certbot/conf:/etc/letsencrypt
      - ../data/certbot/www:/var/www/certbot
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew; sleep 12h & wait $${!}; done;'"

  minio:
    image: minio/minio:latest
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-minioadmin}
      MINIO_DEFAULT_BUCKETS: ${S3_FILE_STORE_BUCKET_NAME:-onyx-file-store-bucket}
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  cache:
    image: redis:7.4-alpine
    restart: unless-stopped
    ports:
      - "6379:6379"
    # docker silently mounts /data even without an explicit volume mount, which enables
    # persistence. explicitly setting save and appendonly forces ephemeral behavior.
    command: redis-server --save "" --appendonly no

volumes:
  db_volume:
  vespa_volume:
  minio_data:
  # Created by the container itself
  model_cache_huggingface:
  indexing_huggingface_model_cache:
  # for logs that we don't want to lose on container restarts
  api_server_logs:
  background_logs:
  inference_model_server_logs:
  indexing_model_server_logs:
