# This env template shows how to configure Onyx for custom multilingual use
# Note that for most use cases it will be enough to configure Onyx multilingual purely through the UI
# See "Search Settings" -> "Advanced" for UI options.
# To use it, copy it to .env in the docker_compose directory (or the equivalent environment settings file for your deployment)

# The following is included with the user prompt. Here's one example but feel free to customize it to your needs:
LANGUAGE_HINT="IMPORTANT: ALWAYS RESPOND IN FRENCH! Even if the documents and the user query are in English, your response must be in French."
LANGUAGE_CHAT_NAMING_HINT="The name of the conversation must be in the same language as the user query."
