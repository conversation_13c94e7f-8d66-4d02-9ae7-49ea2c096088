apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "onyx-stack.fullname" . }}-web-server
  labels:
    {{- include "onyx-stack.labels" . | nindent 4 }}
spec:
  {{- if not .Values.webserver.autoscaling.enabled }}
  replicas: {{ .Values.webserver.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "onyx-stack.selectorLabels" . | nindent 6 }}
      {{- if .Values.webserver.deploymentLabels }}
      {{- toYaml .Values.webserver.deploymentLabels | nindent 6 }}
      {{- end }}
  template:
    metadata:
      {{- with .Values.webserver.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "onyx-stack.labels" . | nindent 8 }}
        {{- with .Values.webserver.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "onyx-stack.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.webserver.podSecurityContext | nindent 8 }}
      containers:
        - name: web-server
          securityContext:
            {{- toYaml .Values.webserver.securityContext | nindent 12 }}
          image: "{{ .Values.webserver.image.repository }}:{{ .Values.webserver.image.tag | default .Values.global.version }}"
          imagePullPolicy: {{ .Values.global.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.webserver.containerPorts.server }}
              protocol: TCP
          resources:
            {{- toYaml .Values.webserver.resources | nindent 12 }}
          envFrom:
            - configMapRef:
                name: {{ .Values.config.envConfigMapName }}
          env:
            {{- include "onyx-stack.envSecrets" . | nindent 12}}
          {{- with .Values.webserver.volumeMounts }}
          volumeMounts:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.webserver.volumes }}
      volumes:
        {{- toYaml . | nindent 8 }}
      {{- end }}
