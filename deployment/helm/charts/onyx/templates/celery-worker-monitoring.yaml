apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "onyx-stack.fullname" . }}-celery-worker-monitoring
  labels:
    {{- include "onyx-stack.labels" . | nindent 4 }}
spec:
  {{- if not .Values.celery_worker_monitoring.autoscaling.enabled }}
  replicas: {{ .Values.celery_worker_monitoring.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "onyx-stack.selectorLabels" . | nindent 6 }}
      {{- if .Values.celery_worker_monitoring.deploymentLabels }}
      {{- toYaml .Values.celery_worker_monitoring.deploymentLabels | nindent 6 }}
      {{- end }}
  template:
    metadata:
      {{- with .Values.celery_worker_monitoring.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "onyx-stack.labels" . | nindent 8 }}
        {{- with .Values.celery_worker_monitoring.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "onyx-stack.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.celery_worker_monitoring.podSecurityContext | nindent 8 }}
      containers:
        - name: celery-worker-monitoring
          securityContext:
            {{- toYaml .Values.celery_worker_monitoring.securityContext | nindent 12 }}
          image: "{{ .Values.celery_shared.image.repository }}:{{ .Values.celery_shared.image.tag | default .Values.global.version }}"
          imagePullPolicy: {{ .Values.global.pullPolicy }}
          command:
            [
              "celery",
              "-A",
              "onyx.background.celery.versioned_apps.monitoring",
              "worker",
              "--loglevel=INFO",
              "--hostname=monitoring@%n",
              "-Q",
              "monitoring",
            ]
          resources:
            {{- toYaml .Values.celery_worker_monitoring.resources | nindent 12 }}
          envFrom:
            - configMapRef:
                name: {{ .Values.config.envConfigMapName }}
          env:
            {{- include "onyx-stack.envSecrets" . | nindent 12}}
          startupProbe:
            {{ .Values.celery_shared.startupProbe | toYaml | nindent 12}}
          readinessProbe:
            {{ .Values.celery_shared.readinessProbe | toYaml | nindent 12}}
            exec:
              command:
                - /bin/bash
                - -c
                - >
                    python onyx/background/celery/celery_k8s_probe.py
                    --probe readiness
                    --filename /tmp/onyx_k8s_monitoring_readiness.txt
          livenessProbe:
            {{ .Values.celery_shared.livenessProbe | toYaml | nindent 12}}
            exec:
              command:
                - /bin/bash
                - -c
                - >
                    python onyx/background/celery/celery_k8s_probe.py
                    --probe liveness
                    --filename /tmp/onyx_k8s_monitoring_liveness.txt
