{{- if .Values.webserver.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "onyx-stack.fullname" . }}-webserver
  labels:
    {{- include "onyx-stack.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "onyx-stack.fullname" . }}
  minReplicas: {{ .Values.webserver.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.webserver.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.webserver.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.webserver.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.webserver.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.webserver.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
