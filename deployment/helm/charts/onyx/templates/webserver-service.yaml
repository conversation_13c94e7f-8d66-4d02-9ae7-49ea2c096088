apiVersion: v1
kind: Service
metadata:
  name: {{ include "onyx-stack.fullname" . }}-webserver
  labels:
    {{- include "onyx-stack.labels" . | nindent 4 }}
    {{- if .Values.webserver.deploymentLabels }}
    {{- toYaml .Values.webserver.deploymentLabels | nindent 4 }}
    {{- end }}
spec:
  type: {{ .Values.webserver.service.type }}
  ports:
    - port: {{ .Values.webserver.service.servicePort }}
      targetPort: {{ .Values.webserver.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "onyx-stack.selectorLabels" . | nindent 4 }}
    {{- if .Values.webserver.deploymentLabels }}
    {{- toYaml .Values.webserver.deploymentLabels | nindent 4 }}
    {{- end }}
