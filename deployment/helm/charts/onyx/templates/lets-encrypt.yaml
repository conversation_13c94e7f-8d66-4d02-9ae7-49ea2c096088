{{- if .Values.letsencrypt.enabled -}}
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: {{ include "onyx-stack.fullname" . }}-letsencrypt
spec:
  acme:
    # The ACME server URL
    server: https://acme-v02.api.letsencrypt.org/directory
    # Email address used for ACME registration
    email: {{ .Values.letsencrypt.email }}
    # Name of a secret used to store the ACME account private key
    privateKeySecretRef:
      name: {{ include "onyx-stack.fullname" . }}-letsencrypt
    # Enable the HTTP-01 challenge provider
    solvers:
      - http01:
          ingress:
            class: nginx
{{- end }}