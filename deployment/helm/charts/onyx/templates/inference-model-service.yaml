apiVersion: v1
kind: Service
metadata:
  name: {{ include "onyx-stack.fullname" . }}-inference-model-service
spec:
  type: {{ .Values.inferenceCapability.service.type }}
  ports:
    - port: {{ .Values.inferenceCapability.service.servicePort}}
      targetPort: {{ .Values.inferenceCapability.service.targetPort }}
      protocol: TCP
      name: {{ .Values.inferenceCapability.service.portName }}
  selector:
    {{- range .Values.inferenceCapability.labels }}
    {{ .key }}: {{ .value }}
    {{- end }}
