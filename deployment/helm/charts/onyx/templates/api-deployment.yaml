apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "onyx-stack.fullname" . }}-api-server
  labels:
    {{- include "onyx-stack.labels" . | nindent 4 }}
spec:
  {{- if not .Values.api.autoscaling.enabled }}
  replicas: {{ .Values.api.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "onyx-stack.selectorLabels" . | nindent 6 }}
      {{- if .Values.api.deploymentLabels }}
      {{- toYaml .Values.api.deploymentLabels | nindent 6 }}
      {{- end }}
  template:
    metadata:
      {{- with .Values.api.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "onyx-stack.labels" . | nindent 8 }}
        {{- with .Values.api.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "onyx-stack.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.api.podSecurityContext | nindent 8 }}
      containers:
        - name: api-server
          securityContext:
            {{- toYaml .Values.api.securityContext | nindent 12 }}
          image: "{{ .Values.api.image.repository }}:{{ .Values.api.image.tag | default .Values.global.version }}"
          imagePullPolicy: {{ .Values.global.pullPolicy }}
          command:
            - "/bin/sh"
            - "-c"
            - |
              alembic upgrade head &&
              echo "Starting Onyx Api Server" &&
              uvicorn onyx.main:app --host 0.0.0.0 --port {{ .Values.api.containerPorts.server }}
          ports:
            - name: api-server-port
              containerPort: {{ .Values.api.containerPorts.server }}
              protocol: TCP
          resources:
            {{- toYaml .Values.api.resources | nindent 12 }}
          envFrom:
            - configMapRef:
                name: {{ .Values.config.envConfigMapName }}
          env:
            {{- include "onyx-stack.envSecrets" . | nindent 12}}
