apiVersion: v1
kind: Service
metadata:
  name: {{ include "onyx-stack.fullname" . }}-indexing-model-service
  labels:
    {{- include "onyx-stack.labels" . | nindent 4 }}
spec:
  selector:
    {{- include "onyx-stack.selectorLabels" . | nindent 4 }}
    {{- if .Values.indexCapability.deploymentLabels }}
    {{- toYaml .Values.indexCapability.deploymentLabels | nindent 4 }}
    {{- end }}
  ports:
    - name: {{ .Values.indexCapability.service.portName }}
      protocol: TCP
      port: {{ .Values.indexCapability.service.servicePort  }}
      targetPort: {{ .Values.indexCapability.service.targetPort }}
  type: {{ .Values.indexCapability.service.type }}
