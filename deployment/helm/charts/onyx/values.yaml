# Default values for onyx-stack.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

global:
  # Global version for all Onyx components (overrides .Chart.AppVersion)
  version: "latest"
  # Global pull policy for all Onyx component images
  pullPolicy: "IfNotPresent"

postgresql:
  primary:
    persistence:
      storageClass: ""
      size: 10Gi
    shmVolume:
      enabled: true
      sizeLimit: 2Gi
  enabled: true
  auth:
    existingSecret: onyx-secrets
    secretKeys:
      # overwriting as postgres typically expects 'postgres-password'
      adminPasswordKey: postgres_password

vespa:
  name: da-vespa-0
  service:
    name: vespa-service
  volumeClaimTemplates:
    - metadata:
        name: vespa-storage
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 30Gi
        storageClassName: ""
  enabled: true
  replicaCount: 1
  image:
    repository: vespa
    tag: "8.526.15"
  podAnnotations: {}
  podLabels:
    app: vespa
    app.kubernetes.io/instance: onyx
    app.kubernetes.io/name: vespa
  securityContext:
    privileged: true
    runAsUser: 0
  resources:
    # The Vespa Helm chart specifies default resources, which are quite modest. We override
    # them here to increase chances of the chart running successfully. If you plan to index at
    # scale, you will likely need to increase these limits further.
    # At large scale, it is recommended to use a dedicated Vespa cluster / Vespa cloud.
    requests:
      cpu: 4000m
      memory: 8000Mi
    limits:
      cpu: 8000m
      memory: 32000Mi

persistent:
  storageClassName: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

inferenceCapability:
  service:
    portName: modelserver
    type: ClusterIP
    servicePort: 9000
    targetPort: 9000
  name: inference-model-server
  replicaCount: 1
  labels:
    - key: app
      value: inference-model-server
  image:
    repository: onyxdotapp/onyx-model-server
    # Overrides the image tag whose default is the chart appVersion.
    tag: ""
  containerPorts:
    server: 9000
  podLabels:
    - key: app
      value: inference-model-server
  resources:
    requests:
      cpu: 2000m
      memory: 6Gi
    limits:
      cpu: 4000m
      memory: 10Gi

indexCapability:
  service:
    portName: modelserver
    type: ClusterIP
    servicePort: 9000
    targetPort: 9000
  replicaCount: 1
  name: indexing-model-server
  deploymentLabels:
    app: indexing-model-server
  podLabels:
    app: indexing-model-server
  indexingOnly: "True"
  podAnnotations: {}
  containerPorts:
    server: 9000
  image:
    repository: onyxdotapp/onyx-model-server
    # Overrides the image tag whose default is the chart appVersion.
    tag: ""
  limitConcurrency: 10
  resources:
    requests:
      cpu: 2000m
      memory: 6Gi
    limits:
      cpu: 4000m
      memory: 10Gi
config:
  envConfigMapName: env-configmap

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

nginx:
  enabled: true
  containerPorts:
    http: 1024
  extraEnvVars:
    - name: DOMAIN
      value: localhost
  service:
    type: LoadBalancer
    ports:
      http: 80
      onyx: 3000
    targetPort:
      http: http
      onyx: http

  existingServerBlockConfigmap: onyx-nginx-conf

webserver:
  replicaCount: 1
  image:
    repository: onyxdotapp/onyx-web-server
    # Overrides the image tag whose default is the chart appVersion.
    tag: ""
  deploymentLabels:
    app: web-server
  podAnnotations: {}
  podLabels:
    app: web-server
  podSecurityContext:
    {}
    # fsGroup: 2000

  securityContext:
    {}
    # capabilities:
    #   drop:
    #   - ALL
    # readOnlyRootFilesystem: true
    # runAsNonRoot: true
    # runAsUser: 1000

  containerPorts:
    server: 3000

  service:
    type: ClusterIP
    servicePort: 3000
    targetPort: http

  resources:
    requests:
      cpu: 200m
      memory: 512Mi
    limits:
      cpu: 1000m
      memory: 1Gi

  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 100
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

  # Additional volumes on the output Deployment definition.
  volumes: []
  # - name: foo
  #   secret:
  #     secretName: mysecret
  #     optional: false

  # Additional volumeMounts on the output Deployment definition.
  volumeMounts: []
  # - name: foo
  #   mountPath: "/etc/foo"
  #   readOnly: true

  nodeSelector: {}
  tolerations: []
  affinity: {}

api:
  replicaCount: 1
  image:
    repository: onyxdotapp/onyx-backend
    # Overrides the image tag whose default is the chart appVersion.
    tag: ""
  deploymentLabels:
    app: api-server
  podAnnotations: {}
  podLabels:
    scope: onyx-backend
    app: api-server

  containerPorts:
    server: 8080

  podSecurityContext:
    {}
    # fsGroup: 2000

  securityContext:
    {}
    # capabilities:
    #   drop:
    #   - ALL
    # readOnlyRootFilesystem: true
    # runAsNonRoot: true
    # runAsUser: 1000

  service:
    type: ClusterIP
    servicePort: 8080
    targetPort: api-server-port
    portName: api-server-port

  resources:
    requests:
      cpu: 1000m
      memory: 1Gi
    limits:
      cpu: 2000m
      memory: 2Gi

  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 100
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

  # Additional volumes on the output Deployment definition.
  volumes: []
  # - name: foo
  #   secret:
  #     secretName: mysecret
  #     optional: false

  # Additional volumeMounts on the output Deployment definition.
  volumeMounts: []
  # - name: foo
  #   mountPath: "/etc/foo"
  #   readOnly: true

  nodeSelector: {}
  tolerations: []


######################################################################
#
# Background workers
#
######################################################################

celery_shared:
  image:
    repository: onyxdotapp/onyx-backend
    tag: ""  # Overrides the image tag whose default is the chart appVersion.
  startupProbe:
    # startupProbe fails after 2m
    exec:
      command: ["test", "-f", "/app/onyx/main.py"]
    failureThreshold: 24
    periodSeconds: 5
    timeoutSeconds: 3
  readinessProbe:
    # readinessProbe fails after 15s + 2m of inactivity
    # it's ok to see the readinessProbe fail transiently while the container starts
    initialDelaySeconds: 15
    periodSeconds: 5
    failureThreshold: 24
    timeoutSeconds: 3
  livenessProbe:
    # livenessProbe fails after 5m of inactivity
    initialDelaySeconds: 60
    periodSeconds: 60
    failureThreshold: 5
    timeoutSeconds: 3

celery_beat:
  replicaCount: 1
  podAnnotations: {}
  podLabels:
    scope: onyx-backend-celery
    app: celery-beat
  deploymentLabels:
    app: celery-beat
  podSecurityContext:
    {}
  securityContext:
    privileged: true
    runAsUser: 0
  resources:
    requests:
      cpu: 1000m
      memory: 1Gi
    limits:
      cpu: 1000m
      memory: 1Gi
  volumes: []  # Additional volumes on the output Deployment definition.
  volumeMounts: []  # Additional volumeMounts on the output Deployment definition.
  nodeSelector: {}
  tolerations: []
  affinity: {}

celery_worker_heavy:
  replicaCount: 1
  autoscaling:
    enabled: false
  podAnnotations: {}
  podLabels:
    scope: onyx-backend-celery
    app: celery-worker-heavy
  deploymentLabels:
    app: celery-worker-heavy
  podSecurityContext:
    {}
  securityContext:
    privileged: true
    runAsUser: 0
  resources:
    requests:
      cpu: 1000m
      memory: 2Gi
    limits:
      cpu: 2500m
      memory: 5Gi
  volumes: []  # Additional volumes on the output Deployment definition.
  volumeMounts: []  # Additional volumeMounts on the output Deployment definition.
  nodeSelector: {}
  tolerations: []
  affinity: {}

celery_worker_docprocessing:
  replicaCount: 1
  autoscaling:
    enabled: false
  podAnnotations: {}
  podLabels:
    scope: onyx-backend-celery
    app: celery-worker-docprocessing
  deploymentLabels:
    app: celery-worker-docprocessing
  podSecurityContext:
    {}
  securityContext:
    privileged: true
    runAsUser: 0
  resources:
    requests:
      cpu: 500m
      memory: 4Gi
    limits:
      cpu: 1000m
      memory: 12Gi
  volumes: []  # Additional volumes on the output Deployment definition.
  volumeMounts: []  # Additional volumeMounts on the output Deployment definition.
  nodeSelector: {}
  tolerations: []
  affinity: {}

celery_worker_light:
  replicaCount: 1
  autoscaling:
    enabled: false
  podAnnotations: {}
  podLabels:
    scope: onyx-backend-celery
    app: celery-worker-light
  deploymentLabels:
    app: celery-worker-light
  podSecurityContext:
    {}
  securityContext:
    privileged: true
    runAsUser: 0
  resources:
    requests:
      cpu: 1000m
      memory: 1Gi
    limits:
      cpu: 2000m
      memory: 4Gi
  volumes: []  # Additional volumes on the output Deployment definition.
  volumeMounts: []  # Additional volumeMounts on the output Deployment definition.
  nodeSelector: {}
  tolerations: []
  affinity: {}

celery_worker_monitoring:
  replicaCount: 1
  autoscaling:
    enabled: false
  podAnnotations: {}
  podLabels:
    scope: onyx-backend-celery
    app: celery-worker-monitoring
  deploymentLabels:
    app: celery-worker-monitoring
  podSecurityContext:
    {}
  securityContext:
    privileged: true
    runAsUser: 0
  resources:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 2000m
      memory: 4Gi
  volumes: []  # Additional volumes on the output Deployment definition.
  volumeMounts: []  # Additional volumeMounts on the output Deployment definition.
  nodeSelector: {}
  tolerations: []
  affinity: {}

celery_worker_primary:
  replicaCount: 1
  autoscaling:
    enabled: false
  podAnnotations: {}
  podLabels:
    scope: onyx-backend-celery
    app: celery-worker-primary
  deploymentLabels:
    app: celery-worker-primary
  podSecurityContext:
    {}
  securityContext:
    privileged: true
    runAsUser: 0
  resources:
    requests:
      cpu: 1000m
      memory: 8Gi
    limits:
      cpu: 2000m
      memory: 16Gi
  volumes: []  # Additional volumes on the output Deployment definition.
  volumeMounts: []  # Additional volumeMounts on the output Deployment definition.
  nodeSelector: {}
  tolerations: []
  affinity: {}

celery_worker_user_files_indexing:
  replicaCount: 1
  autoscaling:
    enabled: false
  podAnnotations: {}
  podLabels:
    scope: onyx-backend-celery
    app: celery-worker-user-files-indexing
  deploymentLabels:
    app: celery-worker-user-files-indexing
  podSecurityContext:
    {}
  securityContext:
    privileged: true
    runAsUser: 0
  resources:
    requests:
      cpu: 2000m
      memory: 6Gi
    limits:
      cpu: 4000m
      memory: 12Gi
  volumes: []  # Additional volumes on the output Deployment definition.
  volumeMounts: []  # Additional volumeMounts on the output Deployment definition.
  nodeSelector: {}
  tolerations: []
  affinity: {}

slackbot:
  enabled: true
  replicaCount: 1
  image:
    repository: onyxdotapp/onyx-backend
    tag: ""  # Overrides the image tag whose default is the chart appVersion.
  podAnnotations: {}
  podLabels:
    scope: onyx-backend
    app: slack-bot
  deploymentLabels:
    app: slack-bot
  podSecurityContext:
    {}
  securityContext:
    {}
  resources:
    requests:
      cpu: "500m"
      memory: "500Mi"
    limits:
      cpu: "1000m"
      memory: "2000Mi"
celery_worker_docfetching:
  replicaCount: 1
  autoscaling:
    enabled: false
  podAnnotations: {}
  podLabels:
    scope: onyx-backend-celery
    app: celery-worker-docfetching
  deploymentLabels:
    app: celery-worker-docfetching
  podSecurityContext:
    {}
  securityContext:
    privileged: true
    runAsUser: 0
  resources:
    requests:
      cpu: 500m
      memory: 8Gi
    limits:
      cpu: 2000m
      memory: 16Gi
  volumes: []  # Additional volumes on the output Deployment definition.
  volumeMounts: []  # Additional volumeMounts on the output Deployment definition.
  nodeSelector: {}
  tolerations: []
  affinity: {}

######################################################################
#
# End background workers section
#
######################################################################

redis:
  enabled: true
  architecture: standalone
  commonConfiguration: |-
    # Enable AOF https://redis.io/topics/persistence#append-only-file
    appendonly no
    # Disable RDB persistence, AOF persistence already enabled.
    save ""
  master:
    replicaCount: 1
    image:
      registry: docker.io
      repository: bitnami/redis
      tag: "7.4.0"
      pullPolicy: IfNotPresent
    persistence:
      enabled: false
  service:
    type: ClusterIP
    port: 6379
  auth:
    existingSecret: onyx-secrets
    existingSecretPasswordKey: redis_password

minio:
  enabled: true
  auth:
    existingSecret: onyx-secrets
    rootUserSecretKey: s3_aws_access_key_id
    rootPasswordSecretKey: s3_aws_secret_access_key
  defaultBuckets: "onyx-file-store-bucket"
  persistence:
    enabled: true
    size: 30Gi
  service:
    type: ClusterIP
    ports:
      api: 9000
      console: 9001
  consoleService:
    type: ClusterIP
    ports:
      http: 9001

ingress:
  enabled: false
  className: ""
  api:
    host: onyx.local
  webserver:
    host: onyx.local

letsencrypt:
  enabled: false
  email: "<EMAIL>"

auth:
  # existingSecret onyx-secret for storing smtp, oauth, slack, and other secrets
  # keys are lowercased version of env vars (e.g. SMTP_USER -> smtp_user)
  existingSecret: ""
  # optionally override the secret keys to reference in the secret
  # this is used to populate the env vars in individual deployments
  # the values here reference the keys in secrets below
  secretKeys:
    postgres_password: "postgres_password"
    smtp_pass: ""
    oauth_client_id: ""
    oauth_client_secret: ""
    oauth_cookie_secret: ""
    redis_password: "redis_password"
    s3_aws_access_key_id: "s3_aws_access_key_id"
    s3_aws_secret_access_key: "s3_aws_secret_access_key"
  # will be overridden by the existingSecret if set
  secretName: "onyx-secrets"
  # set values as strings, they will be base64 encoded
  # this is used to populate the secrets yaml
  secrets:
    postgres_password: "postgres"
    smtp_pass: ""
    oauth_client_id: ""
    oauth_client_secret: ""
    oauth_cookie_secret: ""
    redis_password: "password"
    s3_aws_access_key_id: "minioadmin"
    s3_aws_secret_access_key: "minioadmin"

configMap:
  # Change this for production uses unless Onyx is only accessible behind VPN
  AUTH_TYPE: "disabled"
  # 1 Day Default
  SESSION_EXPIRE_TIME_SECONDS: "86400"
  # Can be something like onyx.app, as an extra double-check
  VALID_EMAIL_DOMAINS: ""
  # For sending verification emails, if unspecified then defaults to 'smtp.gmail.com'
  SMTP_SERVER: ""
  # For sending verification emails, if unspecified then defaults to '587'
  SMTP_PORT: ""
# '<EMAIL>'
  SMTP_USER: ""
  # 'your-gmail-password'
  # SMTP_PASS: ""
  # '<EMAIL>' SMTP_USER missing used instead
  EMAIL_FROM: ""
  # MinIO/S3 Configuration override
  S3_ENDPOINT_URL: ""  # only used if minio is not enabled
  S3_FILE_STORE_BUCKET_NAME: ""
  # Gen AI Settings
  GEN_AI_MAX_TOKENS: ""
  QA_TIMEOUT: "60"
  MAX_CHUNKS_FED_TO_CHAT: ""
  DISABLE_LLM_DOC_RELEVANCE: ""
  DISABLE_LLM_CHOOSE_SEARCH: ""
  DISABLE_LLM_QUERY_REPHRASE: ""
  # Query Options
  DOC_TIME_DECAY: ""
  HYBRID_ALPHA: ""
  EDIT_KEYWORD_QUERY: ""
  MULTILINGUAL_QUERY_EXPANSION: ""
  LANGUAGE_HINT: ""
  LANGUAGE_CHAT_NAMING_HINT: ""
  QA_PROMPT_OVERRIDE: ""
  # Internet Search Tool
  BING_API_KEY: ""
  EXA_API_KEY: ""
  # Don't change the NLP models unless you know what you're doing
  EMBEDDING_BATCH_SIZE: ""
  DOCUMENT_ENCODER_MODEL: ""
  NORMALIZE_EMBEDDINGS: ""
  ASYM_QUERY_PREFIX: ""
  ASYM_PASSAGE_PREFIX: ""
  DISABLE_RERANK_FOR_STREAMING: ""
  MODEL_SERVER_PORT: ""
  MIN_THREADS_ML_MODELS: ""
  # Indexing Configs
  VESPA_SEARCHER_THREADS: ""
  NUM_INDEXING_WORKERS: ""
  DISABLE_INDEX_UPDATE_ON_SWAP: ""
  DASK_JOB_CLIENT_ENABLED: ""
  CONTINUE_ON_CONNECTOR_FAILURE: ""
  EXPERIMENTAL_CHECKPOINTING_ENABLED: ""
  CONFLUENCE_CONNECTOR_LABELS_TO_SKIP: ""
  JIRA_API_VERSION: ""
  GONG_CONNECTOR_START_TIME: ""
  NOTION_CONNECTOR_ENABLE_RECURSIVE_PAGE_LOOKUP: ""
  # Worker Parallelism
  CELERY_WORKER_DOCPROCESSING_CONCURRENCY: ""
  CELERY_WORKER_LIGHT_CONCURRENCY: ""
  CELERY_WORKER_LIGHT_PREFETCH_MULTIPLIER: ""
  # OnyxBot SlackBot Configs
  DANSWER_BOT_DISABLE_DOCS_ONLY_ANSWER: ""
  DANSWER_BOT_DISPLAY_ERROR_MSGS: ""
  DANSWER_BOT_RESPOND_EVERY_CHANNEL: ""
  DANSWER_BOT_DISABLE_COT: ""
  NOTIFY_SLACKBOT_NO_ANSWER: ""
  # Logging
  # Optional Telemetry, please keep it on (nothing sensitive is collected)? <3
  # https://docs.onyx.app/more/telemetry
  DISABLE_TELEMETRY: ""
  LOG_LEVEL: ""
  LOG_ALL_MODEL_INTERACTIONS: ""
  LOG_DANSWER_MODEL_INTERACTIONS: ""
  LOG_VESPA_TIMING_INFORMATION: ""
  # Shared or Non-backend Related
  WEB_DOMAIN: "http://localhost:3000"
  # DOMAIN used by nginx
  DOMAIN: "localhost"
  # Chat Configs
  HARD_DELETE_CHATS: ""
