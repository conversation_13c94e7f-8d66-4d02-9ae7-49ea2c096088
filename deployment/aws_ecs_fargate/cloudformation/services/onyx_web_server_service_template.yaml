AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Onyx Web Server TaskDefinition
Parameters:
  Environment:
    Type: String
  SubnetIDs:
    Type: CommaDelimitedList
    Description: "Comma-delimited list of at least two subnet IDs in different Availability Zones"
  VpcID:
    Type: String
    Default: vpc-098cfa79d637dabff
  ServiceName:
    Type: String
    Default: onyx-web-server
  TaskCpu:
    Type: String
    Default: "1024"
  TaskMemory:
    Type: String
    Default: "2048"
  TaskDesiredCount:
    Type: Number
    Default: 1

Resources:

  ECSService:
    Type: AWS::ECS::Service
    Properties:
      Cluster:
        Fn::ImportValue:
          Fn::Sub: "${Environment}-onyx-cluster-ECSClusterName"
      CapacityProviderStrategy:
        - CapacityProvider: FARGATE
          Base: 0
          Weight: 1
      TaskDefinition: !Ref TaskDefinition
      ServiceName: !Sub ${Environment}-${ServiceName}-service
      SchedulingStrategy: REPLICA
      DesiredCount: !Ref TaskDesiredCount
      AvailabilityZoneRebalancing: ENABLED
      NetworkConfiguration:
        AwsvpcConfiguration:
          AssignPublicIp: ENABLED
          SecurityGroups:
            - Ref: SecurityGroup
          Subnets: !Ref SubnetIDs
      PlatformVersion: LATEST
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 100
        DeploymentCircuitBreaker:
          Enable: true
          Rollback: true
      DeploymentController:
        Type: ECS
      ServiceConnectConfiguration:
        Enabled: false
      ServiceRegistries:
        - RegistryArn: !GetAtt ServiceDiscoveryService.Arn
      Tags:
        - Key: app
          Value: onyx
        - Key: service
          Value: !Ref ServiceName
        - Key: env
          Value: !Ref Environment
      EnableECSManagedTags: true

  SecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: !Sub Onyx SecurityGroup access to EFS mount and ${ServiceName}.
      GroupName: !Sub ${Environment}-ecs-${ServiceName}
      VpcId: !Ref VpcID
      SecurityGroupIngress:
        - FromPort: 3000
          ToPort: 3000
          IpProtocol: tcp
          CidrIp: 0.0.0.0/0
        - FromPort: 3000
          ToPort: 3000
          IpProtocol: tcp
          CidrIpv6: "::/0"

  ServiceDiscoveryService:
    Type: "AWS::ServiceDiscovery::Service"
    Properties:
      Name: !Sub ${Environment}-${ServiceName}-service
      DnsConfig:
        DnsRecords:
          - Type: "A"
            TTL: 15
      NamespaceId:
        Fn::ImportValue:
          Fn::Sub: "${Environment}-onyx-cluster-OnyxNamespace"
      HealthCheckCustomConfig:
        FailureThreshold: 1
  
  TaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub ${Environment}-${ServiceName}-TaskDefinition
      TaskRoleArn:
        Fn::ImportValue:
          Fn::Sub: "${Environment}-onyx-cluster-ECSTaskRole"
      ExecutionRoleArn:
        Fn::ImportValue:
          Fn::Sub: "${Environment}-onyx-cluster-ECSTaskExecutionRole"
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      Cpu: !Ref TaskCpu
      Memory: !Ref TaskMemory
      RuntimePlatform:
        CpuArchitecture: ARM64
        OperatingSystemFamily: LINUX
      ContainerDefinitions:
        - Name: onyx-webserver
          Image: onyxdotapp/onyx-web-server:latest
          Cpu: 0
          Essential: true
          PortMappings:
            - Name: webserver
              ContainerPort: 3000
              HostPort: 3000
              Protocol: tcp
          Environment:
            - Name: NEXT_PUBLIC_DISABLE_STREAMING
              Value: "false"
            - Name: NEXT_PUBLIC_NEW_CHAT_DIRECTS_TO_SAME_PERSONA
              Value: "false"
            - Name: INTERNAL_URL
              Value: !Sub
                - "http://${Environment}-onyx-backend-api-server-service.${ImportedNamespace}:8080"
                - ImportedNamespace: !ImportValue
                    Fn::Sub: "${Environment}-onyx-cluster-OnyxNamespaceName"
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Sub /ecs/${Environment}-${ServiceName}
              mode: non-blocking
              awslogs-create-group: "true"
              max-buffer-size: "25m"
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: ecs
          User: "1000"
          VolumesFrom: []
          SystemControls: []

  ECSAutoScalingTarget:
    Type: AWS::ApplicationAutoScaling::ScalableTarget
    DependsOn: ECSService
    Properties:
      MaxCapacity: 5
      MinCapacity: 1
      ResourceId: !Sub
        - "service/${ImportedCluster}/${Environment}-${ServiceName}-service"
        - ImportedCluster: !ImportValue
            'Fn::Sub': "${Environment}-onyx-cluster-ECSClusterName"
          ServiceName: !Ref ServiceName
          Environment: !Ref Environment
      ScalableDimension: ecs:service:DesiredCount
      ServiceNamespace: ecs

  ECSAutoScalingPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Sub ${Environment}-${ServiceName}-service-cpu-scaleout
      ScalingTargetId: !Ref ECSAutoScalingTarget
      PolicyType: TargetTrackingScaling
      TargetTrackingScalingPolicyConfiguration:
        TargetValue: 75
        PredefinedMetricSpecification:
          PredefinedMetricType: ECSServiceAverageCPUUtilization
        ScaleOutCooldown: 60
        ScaleInCooldown: 60

  ECSAutoScalingPolicyMemory:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Sub ${Environment}-${ServiceName}-service-memory-scaleout
      ScalingTargetId: !Ref ECSAutoScalingTarget
      PolicyType: TargetTrackingScaling
      TargetTrackingScalingPolicyConfiguration:
        TargetValue: 80
        PredefinedMetricSpecification:
          PredefinedMetricType: ECSServiceAverageMemoryUtilization
        ScaleOutCooldown: 60
        ScaleInCooldown: 60
